{"name": "use-element-plus-theme", "private": false, "version": "0.0.5", "author": "qinzhz", "main": "./dist/bundle.mjs", "module": "./dist/bundle.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://gitee.com/cheerwhy/use-element-plus-theme.git"}, "keywords": ["vue3", "element-plus", "theme", "hook"], "scripts": {"dev": "npm -C packages/example run dev", "build": "vue-tsc --noEmit && vite build"}, "dependencies": {"axios": "1.8.4", "element-plus": "^2.3.10", "vite-plugin-dts": "^3.5.2", "vue": "^3.3.4", "dayjs": "1.11.13", "@iconify-json/carbon": "^1.2.10", "@iconify/vue": "^4.1.1", "@interactjs/actions": "^1.10.27", "@interactjs/auto-scroll": "^1.10.27", "@interactjs/auto-start": "^1.10.27", "@interactjs/core": "^1.10.27", "@interactjs/dev-tools": "^1.10.27", "@interactjs/interact": "^1.10.27", "@interactjs/modifiers": "^1.10.27", "@visactor/vtable": "^1.19.1", "@vueuse/core": "11.0.3"}, "devDependencies": {"@types/node": "^18.7.8", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.33.0", "@vitejs/plugin-vue": "^4.3.0", "eslint": "^8.22.0", "eslint-config-prettier": "^8.5.0", "eslint-define-config": "^1.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.3.0", "prettier": "^3.0.0", "sass": "^1.54.0", "typescript": "^4.6.4", "vite": "^4.4.9", "vue-tsc": "^1.8.8"}}