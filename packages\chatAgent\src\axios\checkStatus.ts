/*
 * @Author: wangneng <EMAIL>
 * @Date: 2022-08-01 14:30:52
 * @LastEditors: wangneng <EMAIL>
 * @LastEditTime: 2022-08-01 15:01:40
 * @FilePath: \cpvf-pools\src\utils\axios\checkStatus.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// const stp = projectSetting.sessionTimeoutProcessing;

export function checkStatus(
  status: number,
  msg: string,
  errorMessageMode = 'message',
): void {
  // const { t } = useI18n();
  let errMessage = '';

  switch (status) {
    case 400:
      errMessage = `${msg}`;
      break;
    // 401: Not logged in
    // Jump to the login page if not logged in, and carry the path of the current page
    // Return to the current page after successful login. This step needs to be operated on the login page.
    case 401:
      errMessage = '用户没有权限';

      errMessage = '用户没有权限';
      break;
    case 403:
      errMessage = ''
      break;
    // 404请求不存在
    case 404:
      errMessage = '';
      break;
    case 510:
      errMessage = `${msg}`;
      break;
    default:
      errMessage = '未知错误';
  }

  errMessage = msg || errMessage;

  if (errMessage) {
    if (errorMessageMode === 'modal') {
      // createErrorModal({ title: transformI18n('api.errorTip', true), content: errMessage });
    } else if (errorMessageMode === 'message') {
      if (status === 510) {
        // createMessage.warning(errMessage);
      } else {
        // createMessage.error(errMessage);
      }
    }
  }
}
