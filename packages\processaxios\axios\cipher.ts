import CryptoJS from 'crypto-js';

export function encrypt(encryString: string, keyStr?: string) {
  // 前后端指定同样的秘钥，很重要！！！
  keyStr = keyStr ? keyStr : 'cpitbpmnumberone'; //判断是否存在ksy，不存在就用定义好的key
  const key = CryptoJS.enc.Utf8.parse(keyStr);
  const srcs = CryptoJS.enc.Utf8.parse(encryString);
  const encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });

  return encrypted.toString();
}
export const isOpenEncrypt = false; // 关闭/开启 参数加密
