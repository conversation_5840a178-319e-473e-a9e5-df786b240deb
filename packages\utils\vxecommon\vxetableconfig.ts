import { VxeGridProps } from 'vxe-table';
export const proxyProps = {
  form: true, // 启用表单代理
  props: {
    result: 'result',
    total: 'page.total',
  },
};

export const gridTableConfig = <VxeGridProps>{
  size: 'medium',
  border: false,
  stripe: false,
  // border: true,
  rowConfig: { isCurrent: true, isHover: true },
  // height: '700',
  // maxHeight: '100%',
  minHeight: '700',

  loading: false,
  columnConfig: {
    resizable: true,
  },
  toolbarConfig: {
    refresh: true,
    zoom: true,
    custom: true,
  },
  pagerConfig: {
    pageSize: 10,
    layouts: [
      'Total',
      'PrevJump',
      'PrevPage',
      'JumpNumber',
      'NextPage',
      'NextJump',
      'Sizes',
      'FullJump',
    ],
    background: true,
  },
};
