# 开发环境
NODE_ENV=production
# 配置优先走mock数据还是后端代理
VITE_MOCK=false
# 是否子应用
VITE_MICRO=false

# 接口基础请求地址  通常配置如：http://localhost:9000
VITE_APP_BASE_URL=
# 与VITE_APP_BASE_URL配合 通常配置 如： /api => http://localhost:9000/api
# VITE_APP_BASE_URL_PREFIX=/basic-api
VITE_APP_BASE_URL_PREFIX=http://***********:8080
# 代理
VITE_PROXY = [ ["/api","http://***********:8080/api"]]


# 静态资源地址()uhub
VITE_APP_STATIC_URL=

# 构建资源公共路径
VITE_PUBLIC_PATH=/
