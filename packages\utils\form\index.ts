export function formatFormData(json, formValue) {
  const warehouseData = {};
  const labelData = {};
  const formExtends = json.formConfig.extends;
  if (JSON.stringify(formExtends) !== '{}') {
    const fieldList = json.widgetList;
    const fieldLists = getOneArr(fieldList);
    const list = listFilter(fieldLists);
    for (const key in formExtends) {
      for (let j = 0; j < formExtends[key].length; j++) {
        if (formExtends[key][j] === 'warehouse') {
          warehouseData[key] = formValue[key];
        }
        if (formExtends[key][j] === 'carryLabel') {
          for (let k = 0; k < list.length; k++) {
            if (key === list[k].options.name) {
              labelData[list[k].options.name] = {};
              labelData[list[k].options.name]['label'] = list[k].options.label;
            }
          }
        }
      }
    }
  }
  return { data: formValue, warehouseData, labelData };
}

// const formatFormData = (json, formValue) => {
//   let warehouseData = {};
//   let labelData = {};
//   for (let i = 0; i < json.widgetList.length; i++) {
//     if (json.widgetList[i].type === 'form') {
//       const formExtends = json.widgetList[i].options.extends;
//       if (JSON.stringify(formExtends) !== '{}') {
//         const fieldList = json.widgetList[i].widgetList;
//         const fieldLists = getOneArr(fieldList);
//         const list = listFilter(fieldLists);
//         for (let key in formExtends) {
//           for (let j = 0; j < formExtends[key].length; j++) {
//             if (formExtends[key][j] === 'warehouse') {
//               warehouseData[key] = formValue[key];
//             }
//             if (formExtends[key][j] === 'carryLabel') {
//               for (let k = 0; k < list.length; k++) {
//                 if (key === list[k].options.name) {
//                   labelData[list[k].options.name] = {};
//                   labelData[list[k].options.name]['label'] = list[k].options.label;
//                 }
//               }
//             }
//           }
//         }
//       }
//     }
//   }
//   return { data: formValue, warehouseData, labelData };
// };
const listFilter = (arr) => {
  const list = arr.filter((item: any) => {
    return item.category !== 'container';
  });
  return list;
};
const getOneArr = (arr) => {
  const data = JSON.parse(JSON.stringify(arr));
  const newData: any = [];
  const callback = (item: any) => {
    if (item.tabs) {
      item.widgetList = item.tabs;
    }
    if (item.rows) {
      item.widgetList = item.rows;
    }
    if (item.cols) {
      item.widgetList = item.cols;
    }

    (item.widgetList || (item.widgetList = [])).map((v) => {
      callback(v);
    });
    delete item.widgetList;
    newData.push(item);
  };
  data.map((v) => callback(v));
  return newData;
};
