import { BasicKeys } from './typing';
// import { CacheTypeEnum } from '/@/enums/cacheEnum';
// import { projectSettings } from '/@/settings/config/projectConfig';
import { storageLocal } from './storageProxy';

// const { cacheType, storageName } = projectSettings;

const fn = storageLocal;

export function getStorage<T>(key: BasicKeys) {
  return fn.getItem<T>('CPVF_' + key) as T;
}

export function setStorage(key: BasicKeys, value) {
  return fn.setItem('CPVF_' + key, value);
}

export function removeStorage(key: BasicKeys) {
  return fn.removeItem(key);
}

export function clearStorage() {
  return fn.clear();
}
