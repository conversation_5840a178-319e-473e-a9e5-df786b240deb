export const dataSources = [
  {
    value: 'database',
    label: '数据源',
    children: [
      { value: 'mysql', label: 'MySQL' },
      { value: 'postgresql', label: 'PostgreSQL' }
    ]
  }
];

// AI模型选项
export const aiModels = [
  { value: 'qwen2.5-coder', label: 'qwen2.5-coder' },
  { value: 'deepseek-v3', label: 'deepseek-v3' },
];
export const chatList = {
  "success": true,
  "errorCode": null,
  "errorMessage": null,
  "data": {
    "data": [
      {
        "id": 70927,
        "title": "生成报告，不同性别学生的成绩分布",
        "organizationId": 71435,
        "userId": 6708924,
        "dataSourceId": null,
        "dataSourceCollectionId": null,
        "databaseName": null,
        "schemaName": null,
        "tableNames": null,
        "modelName": "",
        "viewShareId": "f164aed7c50d4f1e9be99dc38d751671",
        "editShareId": "ce2c3e2848434d729da2390bfbbf4e52",
        "source": "DASHBOARD_DRAWER_CHAT",
        "chatDetails": null,
        "editable": true,
        "dataSourceCollectionVO": null,
        "fileName": null,
        "filePath": null
      },
      {
        "id": 70922,
        "title": "生成报告，不同性别学生的成绩分布",
        "organizationId": 71435,
        "userId": 6708924,
        "dataSourceId": null,
        "dataSourceCollectionId": null,
        "databaseName": null,
        "schemaName": null,
        "tableNames": null,
        "modelName": "",
        "viewShareId": "5f6744dc8c39456495df598f593c6290",
        "editShareId": "84d6d2193dab4c70b5db9dd9edf880ef",
        "source": "DRAWER_CHAT",
        "chatDetails": null,
        "editable": true,
        "dataSourceCollectionVO": null,
        "fileName": null,
        "filePath": null
      },
      {
        "id": 70882,
        "title": "Chat with Demo data",
        "organizationId": 71435,
        "userId": 6708924,
        "dataSourceId": 98699,
        "dataSourceCollectionId": null,
        "databaseName": null,
        "schemaName": null,
        "tableNames": null,
        "modelName": "",
        "viewShareId": "114e1c5d311d4409974e344c9677ae19",
        "editShareId": "9cfca9a8ba104974b1ad8502fbffbea3",
        "source": "DATASOURCE_CONSOLE_CHAT",
        "chatDetails": null,
        "editable": true,
        "dataSourceCollectionVO": null,
        "fileName": null,
        "filePath": null
      }

    ],
    "pageNo": 1,
    "pageSize": 20,
    "total": 18,
    "hasNextPage": false
  },
  "traceId": null,
  "errorDetail": null,
  "solutionLink": null,
  "hasNextPage": false
}