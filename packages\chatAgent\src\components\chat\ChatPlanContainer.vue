<!--
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
-->
<template>
  <div class="plan-container">
    <div class="plan-header">
      <Icon icon="carbon:flow" />
      <div class="plan-title">{{ planData.title }}</div>
      <div class="plan-status" :class="getStatusClass(planData.status)">
        {{ getStatusText(planData.status) }}
      </div>
    </div>

    <div class="plan-content">
      <!-- 计划信息 -->
      <div class="plan-info">
        <div v-if="planData.userRequest">
          <strong>用户请求:</strong> {{ planData.userRequest }}
        </div>
        <div v-if="planData.startTime">
          <strong>开始时间:</strong> {{ formatTime(planData.startTime) }}
        </div>
        <div v-if="planData.endTime">
          <strong>结束时间:</strong> {{ formatTime(planData.endTime) }}
        </div>
      </div>

      <!-- 步骤信息 - 兼容原始HTML格式 -->
      <div class="plan-steps" v-if="planData.steps && planData.steps.length > 0">
        <div
          v-for="(step, index) in planData.steps"
          :key="index"
          class="plan-step"
          :class="getStepStatusFromText(step)"
        >
          <span>{{ getStepIcon(step) }}</span>
          <span>{{ step }}</span>
        </div>
      </div>

      <!-- 智能体容器 -->
      <div v-if="planData.agents && planData.agents.length > 0" class="agents-container">
        <ChatAgentContainer
          v-for="agent in planData.agents"
          :key="agent.id"
          :agent-data="agent"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import ChatAgentContainer from './ChatAgentContainer.vue'

interface AgentData {
  id: string
  name: string
  status: string
  // 其他智能体属性
}

interface PlanData {
  id: string
  title: string
  status: 'not-started' | 'in-progress' | 'completed'
  userRequest?: string
  startTime?: string | Date
  endTime?: string | Date
  steps?: string[] // 兼容原始HTML格式，步骤是字符串数组
  currentStepIndex?: number
  agents?: AgentData[]
}

defineProps<{
  planData: PlanData
}>()

const getStatusClass = (status: string) => {
  return `status-${status}`
}

const getStatusText = (status: string) => {
  const statusMap = {
    'not-started': '未开始',
    'in-progress': '执行中',
    'completed': '已完成'
  }
  return statusMap[status] || status
}

// 从步骤文本中解析状态（兼容原始HTML格式）
const getStepStatusFromText = (step: string) => {
  if (step.includes('[in_progress]')) return 'in-progress'
  if (step.includes('[completed]')) return 'completed'
  return 'not-started'
}

// 根据步骤文本获取图标
const getStepIcon = (step: string) => {
  if (step.includes('[in_progress]')) return '🔄'
  if (step.includes('[completed]')) return '✅'
  return '⏳'
}

// 格式化时间
const formatTime = (time: string | Date) => {
  if (!time) return '未知'
  const date = typeof time === 'string' ? new Date(time) : time
  return date.toLocaleString()
}
</script>

<style lang="less" scoped>
.plan-container {
  margin: 20px 0;
  border: 2px solid #6366f1;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(99, 102, 241, 0.02));
  overflow: hidden;
  animation: planSlideIn 0.5s ease;
}

@keyframes planSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.plan-header {
  background: #6366f1;
  color: white;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
}

.plan-title {
  flex: 1;
  font-size: 1.1rem;
  line-height: 1.4;
}

.plan-status {
  font-size: 0.875rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;

  &.status-not-started {
    background: rgba(107, 114, 128, 0.2);
  }

  &.status-in-progress {
    background: rgba(251, 191, 36, 0.2);
  }

  &.status-completed {
    background: rgba(16, 185, 129, 0.2);
  }
}

.plan-content {
  padding: 20px;
}

.plan-info {
  background: rgba(99, 102, 241, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  font-size: 0.875rem;
  border-left: 4px solid #6366f1;
}

.plan-steps {
  margin: 16px 0;
}

.plan-step {
  padding: 8px 12px;
  margin: 4px 0;
  border-radius: 6px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 8px;

  &.not-started {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
  }

  &.in-progress {
    background: rgba(251, 191, 36, 0.1);
    color: #f59e0b;
    border-left: 3px solid #f59e0b;
  }

  &.completed {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border-left: 3px solid #10b981;
  }
}

.agents-container {
  margin-top: 16px;
}
</style>
