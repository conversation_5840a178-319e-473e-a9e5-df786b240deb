import { getStorage, setStorage } from './../../storage';
import { TOKEN_KEY, USER_INFO_KEY } from './../../enums/cacheEnum';
import { UserInfo } from './../../types/store';

export function getAuthToken(): string {
  return getStorage(TOKEN_KEY);
}

export function setAuthToken(value: string) {
  setStorage(TOKEN_KEY, value);
}

export function getAuthStorage(): UserInfo {
  return getStorage(USER_INFO_KEY);
}

export function setAuthStorage(value: UserInfo) {
  return setStorage(USER_INFO_KEY, value);
}
