import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import viteCompression from 'vite-plugin-compression';
import { visualizer } from 'rollup-plugin-visualizer';
import { warpperEnv } from './build';
import { createProxy } from './build/vite';
// 基础配置
const baseConfig = {
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  }
};
// 当前执行node命令时文件夹的地址（工作目录）
const root: string = process.cwd();
// 根据命令行参数判断构建模式
export default defineConfig(({ command, mode }) => {
  const isLib = mode === 'lib';
  const { VITE_PORT, VITE_PROXY } = warpperEnv(
    loadEnv(mode, root),
  );
  return {
    ...baseConfig,
    server: {
      port: VITE_PORT,
      host: '0.0.0.0',
      // open: true,
      cors: true,
      proxy: createProxy(VITE_PROXY),
    },
    plugins: [
      ...baseConfig.plugins,
      viteCompression({
        verbose: true,
        disable: false,
        threshold: 10240,
        algorithm: 'gzip',
        ext: '.gz',
      }),
      visualizer({
        open: false,
        gzipSize: true,
        brotliSize: true,
      }),
    ],
  build: isLib ? {
    // 组件库模式构建配置
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'ChatAIUI',
      fileName: (format) => `chat-ai-ui.${format}.js`
    },
    cssCodeSplit: true,
    minify: 'esbuild',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      // 将所有依赖都设置为外部依赖，减小包体积
      external: [
        'vue', 
        'element-plus', 
        '@element-plus/icons-vue',
        'echarts', 
        'marked', 
        'highlight.js',
        'monaco-editor',
        '@visactor/vtable'
      ],
      // 禁用代码分割，避免与manualChunks冲突
      inlineDynamicImports: true,
      output: {
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        globals: {
          vue: 'Vue',
          'element-plus': 'ElementPlus',
          '@element-plus/icons-vue': 'ElementPlusIconsVue',
          'echarts': 'echarts',
          'marked': 'marked',
          'highlight.js': 'hljs',
          'monaco-editor': 'monaco',
          '@visactor/vtable': 'VTable'
        }
      }
    }
  } : {
    // 应用部署模式构建配置
    outDir: 'dist-app',
    minify: 'esbuild',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
      },
      output: {
        // 静态资源分类打包
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            // 根据包名分割代码
            return id.toString().split('node_modules/')[1].split('/')[0].toString();
          }
        },
      }
    },
    // 启用CSS代码分割
    cssCodeSplit: true,
    // 启用源码映射
    sourcemap: false,
    // 启用CSS压缩
    cssMinify: true,
  }
}
});