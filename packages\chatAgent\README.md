# ChatBI 项目

## Axios 功能使用说明

本项目已集成 Axios 用于处理 HTTP 请求，提供了以下功能：

### 1. HTTP 请求封装

在 `src/utils/http.ts` 中封装了 Axios 实例，包括：

- 基础配置（baseURL, timeout 等）
- 请求拦截器（可用于添加 token 等认证信息）
- 响应拦截器（统一处理响应和错误）
- 封装的请求方法（get, post, put, delete）

### 2. API 服务

在 `src/api/index.ts` 中组织了 API 请求，按功能模块分类：

- 数据源相关 API（dataSourceApi）
- AI 对话相关 API（chatApi）
- SQL 相关 API（sqlApi）

### 3. 使用示例

```typescript
// 导入 API
import { dataSourceApi, chatApi, sqlApi } from '@/api';

// 使用 API
const getDataSources = async () => {
  try {
    const res = await dataSourceApi.getList();
    console.log('数据源列表:', res.data);
  } catch (error) {
    console.error('获取数据源列表失败:', error);
  }
};
```

### 4. 示例组件

`src/components/ApiExample.vue` 提供了一个完整的示例组件，展示了如何在 Vue 组件中使用 API 服务。

### 5. 环境变量配置

在 `.env` 文件中配置 API 基础 URL：

```
VITE_API_BASE_URL=http://localhost:3000
```

可以创建 `.env.development`、`.env.production` 等文件来针对不同环境配置不同的 API 地址。

### 6. 类型支持

所有 API 请求和响应都有完整的 TypeScript 类型支持，提供更好的开发体验和代码提示。