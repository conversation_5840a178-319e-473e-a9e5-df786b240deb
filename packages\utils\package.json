{"name": "@chat-agent/utils", "private": false, "version": "1.0.0", "type": "module", "main": "./dist/utils.es.js", "scripts": {"lib": "vite build --config vite-lib.config.js", "pub": "npm publish --registry  http://************:8081/repository/npm-cpvf"}, "files": ["dist"], "dependencies": {"@rollup/plugin-commonjs": "21.1.0", "axios": "1.8.4", "qs": "^6.11.0", "unplugin-vue-components": "^0.22.8", "vite-plugin-compression": "^0.5.1"}, "devDependencies": {"typescript": "^4.6.4", "vite": "2.9.14"}, "gitHead": "ce69f60d26bb5075d3fe15d60e4a1b51523ef7f5"}