/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { getAuthToken } from '@chat-agent/utils';
// 新的流式请求接口
export interface DirectStreamingRequest {
  chatId: string
  // userId: string
  message: string
  toolContext?: Record<string, any>
  modelId?: string
}

// 流式事件接口
export interface DirectStreamEvent {
  type: string
  planId?: string
  entityId?: string
  payload: string | any
  compressed?: boolean
}

export class DirectApiService {
  private static readonly BASE_URL = '/api/executor'
  private static readonly DIRECT_STREAMING_URL = '/api/streaming-events'

  // 直接发送任务（直接执行模式）
  public static async sendMessage(query: string): Promise<any> {
    const response = await fetch(`${this.BASE_URL}/execute`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query })
    })
    if (!response.ok) throw new Error(`API请求失败: ${response.status}`)
    return await response.json()
  }

  /**
   * 发送消息并获取流式响应（使用新的/direct端点）
   * @param planTemplateId 计划模板ID
   * @param request 请求体
   * @param onEvent 事件处理回调
   * @param onError 错误处理回调
   * @param onComplete 完成回调
   * @param timeout 超时时间（毫秒），默认5分钟
   */
  public static async sendMessageWithStreaming(
    planTemplateId: string,
    request: DirectStreamingRequest,
    onEvent: (event: DirectStreamEvent) => void,
    onError: (error: Error) => void,
    onComplete: () => void,
    timeout: number = 5 * 60 * 1000, // 默认5分钟超时
    externalAbortController?: AbortController // 可选的外部AbortController
  ): Promise<void> {
    let timeoutId: NodeJS.Timeout | null = null
    let reader: ReadableStreamDefaultReader<Uint8Array> | null = null
    let isCompleted = false

    try {
      console.log('🚀 发送新的流式消息请求:', { planTemplateId, request, timeout })

      // 构建新的API URL - 使用/direct端点
      const url = `${this.DIRECT_STREAMING_URL}/template/${encodeURIComponent(planTemplateId)}/direct`

      // 使用外部提供的AbortController或创建新的
      const abortController = externalAbortController || new AbortController()

      // 设置超时
      timeoutId = setTimeout(() => {
        if (!isCompleted) {
          console.warn('⏰ 流式请求超时，正在取消...')
          abortController.abort()
          if (!isCompleted) {
            isCompleted = true
            onError(new Error(`流式请求超时 (${timeout}ms)`))
          }
        }
      }, timeout)

      // 发送POST请求
      const response = await fetch(url, {
        method: 'POST',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Authorization': `${getAuthToken()}`
        },
        body: JSON.stringify(request),
        signal: abortController.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      console.log('✅ POST请求成功，开始读取流式响应')

      // 获取响应的reader
      const responseReader = response.body?.getReader()
      if (!responseReader) {
        throw new Error('无法获取响应流')
      }
      reader = responseReader

      const decoder = new TextDecoder()
      let buffer = '' // 用于累积不完整的数据
      let lastActivityTime = Date.now()
      const activityTimeout = 30000*10 // 30秒无数据则认为连接异常

      // 处理流式响应
      const readStream = async (): Promise<void> => {
        try {
          // 检查是否已完成
          if (isCompleted) {
            return
          }

          const { done, value } = await reader!.read()

          if (done) {
            console.log('✅ 流式响应完成 (done=true)')

            // 处理缓冲区中剩余的数据
            if (buffer.trim()) {
              this.processSSEChunk(buffer, onEvent)
            }

            if (!isCompleted) {
              isCompleted = true
              if (timeoutId) {
                clearTimeout(timeoutId)
              }
              onComplete()
            }
            return
          }

          // 更新活动时间
          lastActivityTime = Date.now()

          // 解码数据并添加到缓冲区
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          console.log('🔄 收到数据块:', chunk)

          // 检查是否收到结束信号
          if (chunk.includes('[DONE]') || chunk.includes('data: [DONE]')) {
            console.log('✅ 收到结束信号 [DONE]')

            // 处理缓冲区中剩余的数据（排除结束信号）
            const finalBuffer = buffer.replace(/\[DONE\]/g, '').trim()
            if (finalBuffer) {
              this.processSSEChunk(finalBuffer, onEvent)
            }

            if (!isCompleted) {
              isCompleted = true
              if (timeoutId) {
                clearTimeout(timeoutId)
              }
              onComplete()
            }
            return
          }

          // 按行分割处理完整的事件
          const lines = buffer.split('\n')

          // 保留最后一行（可能不完整）
          buffer = lines.pop() || ''

          // 处理完整的行 - 逐行处理确保实时性
          for (const line of lines) {
            if (line.trim()) {
              this.processSSEChunk(line + '\n', onEvent)
            }
          }

          // 检查活动超时
          if (Date.now() - lastActivityTime > activityTimeout) {
            throw new Error('流式连接无响应超时')
          }

          // 立即继续读取下一块数据，确保实时处理
          return readStream()
        } catch (error) {
          if (!isCompleted) {
            isCompleted = true
            if (timeoutId) {
              clearTimeout(timeoutId)
            }
            console.error('❌ 读取流式响应失败:', error)
            onError(error as Error)
          }
        }
      }

      await readStream()
    } catch (error) {
      if (!isCompleted) {
        isCompleted = true
        if (timeoutId) {
          clearTimeout(timeoutId)
        }
        // console.error('❌ 发送流式请求失败:', error)
        onError(error as Error)
      }
    } finally {
      // 清理资源
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      if (reader) {
        try {
          reader.cancel()
        } catch (e) {
          console.warn('清理reader时出错:', e)
        }
      }
    }
  }

  /**
   * 处理SSE数据块
   */
  private static processSSEChunk(chunk: string, onEvent: (event: DirectStreamEvent) => void): void {
    console.log('🔍 处理SSE数据块:', chunk)

    const lines = chunk.split('\n')

    for (const line of lines) {
      const trimmedLine = line.trim()

      if (trimmedLine.startsWith('data:')) {
        const eventData = trimmedLine.substring(5).trim() // 移除 "data:" 前缀并去除空格

        if (eventData && eventData !== '') {
          try {
            console.log('📨 解析SSE事件数据:', eventData)
            const parsedData = JSON.parse(eventData)
            onEvent(parsedData)
          } catch (e) {
            console.error('❌ 解析SSE事件失败:', e, eventData)
          }
        }
      } else if (trimmedLine === '') {
        // 空行表示事件结束，这是SSE标准格式
        continue
      } else if (trimmedLine.startsWith('event:') || trimmedLine.startsWith('id:') || trimmedLine.startsWith('retry:')) {
        // 其他SSE字段，暂时忽略
        console.log('📋 SSE元数据:', trimmedLine)
      }
    }
  }
}
