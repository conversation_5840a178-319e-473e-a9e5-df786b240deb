/**
 * 权限模式
 */
export enum PermissionModeEnum {
  // 纯前端路由
  FRONT_END = 'FRONT_END',
  // 前后结合路由
  ROUTE_MAPPING = 'ROUTE_MAPPING',
}
/**
 * @description: i18n type
 */
export enum I18nTypeEnum {
  none = 'none', // 不展示切换
  zh = 'zh', // 中文
  en = 'en', // 英文
}

export enum ThemeTypeEnum {
  none = 'none', // 不展示切换
  default = 'default', // 默认
  red = 'red', // 红色
}

export enum Layout {
  VERTICAL = 'vertical',
  HORIZONTAL = 'horizontal',
}
