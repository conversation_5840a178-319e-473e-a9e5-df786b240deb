import { BasicKeys } from './../storage/typing';
// import { projectSettings } from '/@/settings/config/projectConfig';
import { storageLocal } from './../storage/storageProxy';

// const { cacheType, storageName } = projectSettings;

const fn = storageLocal;

export function getStorage<T>(key: BasicKeys) {
  return fn.getItem<T>('CPVF_' + key) as T;
}

export function setStorage(key: BasicKeys, value) {
  return fn.setItem('CPVF_' + key, value);
}

export function removeStorage(key: BasicKeys) {
  return fn.removeItem(key);
}

export function clearStorage() {
  return fn.clear();
}
