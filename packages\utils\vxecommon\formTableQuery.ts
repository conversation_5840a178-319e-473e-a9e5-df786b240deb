interface ProxyAjaxQueryPageParams {
  total: number;
  pageSize: number;
  currentPage: number;
}
type paramsType = {
  id?: string;
  type?: string;
  [key: string]: string | undefined;
};
export function formTableQuery(
  page: ProxyAjaxQueryPageParams,
  form: any, // 表格查询
  api: any, // 请求
  argums: paramsType,
) {
  const queryParams: any = Object.assign({}, form);
  let params = {
    pageNum: page.currentPage,
    pageSize: page.pageSize,
  };
  params = Object.assign({}, argums, params);

  const totalParams = Object.assign({}, queryParams, params);
  // console.log('totalParams', totalParams);

  return new Promise(async (resolve) => {
    // 表格请求列表
    const res = argums.id ? await api(argums.id, totalParams) : await api(totalParams);
    resolve({
      page: {
        total: res.total,
        layouts:
          "['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'Total']",
      },
      result: res.records,
    });
  });
}
