/**
 * 模拟EventSource服务器
 * 用于在前端模拟服务器发送事件，实现流式响应效果
 */

/**
 * 模拟EventSource类
 * 实现与原生EventSource类似的API
 */
export class MockEventSource extends EventTarget {
  private url: string;
  private options?: any;
  private readyState: number;
  private intervalId: number | null = null;
  private chunks: string[];
  private currentIndex: number = 0;
  private speed: number;

  // EventSource 状态常量
  static readonly CONNECTING = 0;
  static readonly OPEN = 1;
  static readonly CLOSED = 2;

  constructor(url: string, options?: any, speed: number = 50) {
    super();
    this.url = url;
    this.options = options;
    this.readyState = MockEventSource.CONNECTING;
    this.chunks = [];
    this.speed = speed;
    
    // 模拟连接过程
    setTimeout(() => {
      this.readyState = MockEventSource.OPEN;
      this.dispatchEvent(new Event('open'));
    }, 100);
  }

  /**
   * 设置要发送的文本内容
   * @param text 完整文本
   */
  setContent(text: string, chunkSize: number = 3) {
    this.chunks = this.splitTextIntoChunks(text, chunkSize);
    this.currentIndex = 0;
    
    // 开始发送事件
    this.startStreaming();
  }

  /**
   * 将文本分割成小块
   * @param text 完整文本
   * @param chunkSize 每块的大小
   */
  private splitTextIntoChunks(text: string, chunkSize: number = 3): string[] {
    const chunks: string[] = [];
    let index = 0;
    
    while (index < text.length) {
      // 确保不会在单词中间分割
      let end = Math.min(index + chunkSize, text.length);
      if (end < text.length && text[end] !== ' ' && text[end - 1] !== ' ') {
        // 尝试找到下一个空格
        const nextSpace = text.indexOf(' ', end);
        if (nextSpace !== -1 && nextSpace - end < 10) { // 如果空格不太远
          end = nextSpace;
        }
      }
      
      chunks.push(text.substring(index, end));
      index = end;
    }
    
    return chunks;
  }

  /**
   * 开始流式发送事件
   */
  private startStreaming() {
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
    }

    this.intervalId = window.setInterval(() => {
      if (this.currentIndex < this.chunks.length) {
        const chunk = this.chunks[this.currentIndex];
        
        // 创建自定义事件
        const messageEvent = new MessageEvent('message', {
          data: JSON.stringify({
            text: chunk,
            done: this.currentIndex === this.chunks.length - 1
          })
        });
        
        // 分发事件
        this.dispatchEvent(messageEvent);
        
        this.currentIndex++;
      } else {
        // 所有块都已发送，关闭连接
        this.close();
      }
    }, this.speed);
  }

  /**
   * 关闭连接
   */
  close() {
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    this.readyState = MockEventSource.CLOSED;
    this.dispatchEvent(new Event('close'));
  }
}

/**
 * 创建模拟的EventSource实例
 * @param text 要流式发送的完整文本
 * @param speed 发送速度（毫秒/块）
 */
export function createMockEventSource(text: string, speed: number = 50): MockEventSource {
  const eventSource = new MockEventSource('/mock-stream', {}, speed);
  eventSource.setContent(text);
  return eventSource;
}