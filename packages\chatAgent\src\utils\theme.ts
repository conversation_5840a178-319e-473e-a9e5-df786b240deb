export const colorList = {
    "polar-blue": "#587df1",
    "golden-purple": "#9373ee",
    "blue2": "#00c3ee",
    "polar-green": "#039e74",
    "gold": "#9a7d56",
    "silver": "#8e8374",
    "red": "#fd6874",   
    "orange": "#fa8c16",
}

// [
//     {
//       code: 'golden-purple',
//       color: '#9373ee',
//     },
//     {
//       code: 'polar-blue',
//       color: '#1a90ff',
//     },
//     {
//       code: 'blue2',
//       color: '#00c3ee',
//     },
//     {
//       code: 'polar-green',
//       color: '#039e74',
//     },
//     {
//       code: 'gold',
//       color: '#9a7d56',
//     },
//     {
//       code: 'silver',
//       color: '#8e8374',
//     },
//     {
//       code: 'red',
//       color: '#fd6874',
//     },
//     {
//       code: 'orange',
//       color: '#fa8c16',
//     },
//   ];

export const mix = (color1: string, color2: string, weight: number) => {
    weight = Math.max(Math.min(Number(weight), 1), 0)
    const r1 = parseInt(color1.substring(1, 3), 16)
    const g1 = parseInt(color1.substring(3, 5), 16)
    const b1 = parseInt(color1.substring(5, 7), 16)
    const r2 = parseInt(color2.substring(1, 3), 16)
    const g2 = parseInt(color2.substring(3, 5), 16)
    const b2 = parseInt(color2.substring(5, 7), 16)
    const r = Math.round(r1 * (1 - weight) + r2 * weight)
    const g = Math.round(g1 * (1 - weight) + g2 * weight)
    const b = Math.round(b1 * (1 - weight) + b2 * weight)
    const _r = ('0' + (r || 0).toString(16)).slice(-2)
    const _g = ('0' + (g || 0).toString(16)).slice(-2)
    const _b = ('0' + (b || 0).toString(16)).slice(-2)
    return '#' + _r + _g + _b
  }