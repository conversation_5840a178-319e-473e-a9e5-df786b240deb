{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM"], "moduleResolution": "Node", "strict": true, "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "noEmit": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "skipLibCheck": true, "jsx": "preserve"}, "include": ["packages/**/*.ts", "packages/**/*.d.ts", "packages/**/*.vue", "vite-env.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}