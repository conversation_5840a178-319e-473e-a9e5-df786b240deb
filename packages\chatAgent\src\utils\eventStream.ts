/**
 * 事件流模拟工具
 * 用于将完整回答分割成小块，通过定时器模拟流式传输
 */

/**
 * 将文本分割成小块
 * @param text 完整文本
 * @param chunkSize 每块的大小
 */
export function splitTextIntoChunks(text: string, chunkSize: number = 3): string[] {
  const chunks: string[] = [];
  let index = 0;
  
  while (index < text.length) {
    // 确保不会在单词中间分割
    let end = Math.min(index + chunkSize, text.length);
    if (end < text.length && text[end] !== ' ' && text[end - 1] !== ' ') {
      // 尝试找到下一个空格
      const nextSpace = text.indexOf(' ', end);
      if (nextSpace !== -1 && nextSpace - end < 10) { // 如果空格不太远
        end = nextSpace;
      }
    }
    
    chunks.push(text.substring(index, end));
    index = end;
  }
  
  return chunks;
}

/**
 * 模拟事件流响应
 * @param fullText 完整回答文本
 * @param onChunk 每次接收到文本块的回调
 * @param onComplete 完成时的回调
 * @param speed 打字速度（毫秒/块）
 */
export function simulateEventStream(
  fullText: string,
  onChunk: (chunk: string, accumulated: string) => void,
  onComplete?: () => void,
  speed: number = 50
): { cancel: () => void } {
  const chunks = splitTextIntoChunks(fullText);
  let accumulatedText = '';
  let currentIndex = 0;
  let timerId: number | null = null;
  
  const processNextChunk = () => {
    if (currentIndex < chunks.length) {
      const chunk = chunks[currentIndex];
      accumulatedText += chunk;
      
      onChunk(chunk, accumulatedText);
      currentIndex++;
      
      timerId = window.setTimeout(processNextChunk, speed);
    } else {
      if (onComplete) {
        onComplete();
      }
    }
  };
  
  // 开始处理
  processNextChunk();
  
  // 返回取消函数
  return {
    cancel: () => {
      if (timerId !== null) {
        clearTimeout(timerId);
      }
    }
  };
}

/**
 * 创建一个新的聊天对话
 * @param title 对话标题
 */
export function createNewChat(title: string) {
  return {
    id: Date.now(),
    title,
    organizationId: 0,
    userId: 0,
    chatDetails: []
  };
}

/**
 * 创建一个新的问题
 * @param content 问题内容
 */
export function createQuestion(content: string) {
  return {
    id: Date.now(),
    content,
    type: "ORDINARY_CHAT",
    extParams: null
  };
}

/**
 * 创建一个新的回答
 * @param questionId 问题ID
 */
export function createAnswer(questionId: number) {
  return {
    id: Date.now(),
    questionId,
    content: "",
    type: "CONTENT",
    goodFeedback: false,
    badFeedback: false,
    noFeedBack: true,
    regenerate: false,
    parts: []
  };
}

/**
 * 创建一个Markdown部分
 * @param answerId 回答ID
 * @param questionId 问题ID
 */
export function createMarkdownPart(answerId: number, questionId: number) {
  return {
    id: Date.now(),
    answerId,
    questionId,
    partType: "MARKDOWN",
    text: "",
    metaData: {
      headerList: null,
      dataList: null
    },
    tableMap: null,
    functionCall: "null",
    chartSchema: null,
    databaseInfo: {
      dataSourceId: null,
      alias: null,
      databaseName: null,
      schemaName: null,
      databaseType: null,
      tableName: null,
      sql: null
    },
    step: 0,
    status: "STREAMING", // 初始状态为流式传输中
    errorCode: null,
    errorMsg: null,
    createTime: Date.now(),
    modifyTime: Date.now(),
    recommends: null
  };
}