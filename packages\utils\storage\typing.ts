import {
  TOKEN_KEY,
  HEADER_TENANT_PROJECT_KEY,
  DICT_KEY,
  USER_INFO_KEY,
  PERMISSON_CODE_KEY,
} from '../cacheEnum';
import { UserInfo } from '../store.d';
export interface Dict {
  label: string;
  value: string;
}
export interface ResDict {
  [code: string]: Dict[];
}

interface BasicStore {
  [TOKEN_KEY]: string | number | null | undefined;
  [USER_INFO_KEY]: UserInfo;
  [HEADER_TENANT_PROJECT_KEY]: string;
  [DICT_KEY]: ResDict;
  [PERMISSON_CODE_KEY]: string | number | null | undefined;
}

// 限定BasicKeys的类型为BasicStore的key TOKEN_KEY | USER_INFO_KEY
export type BasicKeys = keyof BasicStore;
