export function intervalTime(milliseconds: number) {
  // 计算天数
  const days = Math.floor(milliseconds / (24 * 60 * 60 * 1000));
  milliseconds %= 24 * 60 * 60 * 1000;

  // 计算小时数
  const hours = Math.floor(milliseconds / (60 * 60 * 1000));
  milliseconds %= 60 * 60 * 1000;

  // 计算分钟数
  const minutes = Math.floor(milliseconds / (60 * 1000));
  milliseconds %= 60 * 1000;

  // 计算秒数
  const seconds = Math.floor(milliseconds / 1000);
  milliseconds %= 1000;

  // 返回格式化的时间
  // return `${days}天 ${hours}小时 ${minutes}分钟 ${seconds}秒 ${milliseconds}毫秒`;
  return (
    (days > 0 ? days + '天' : '') +
    (hours > 0 ? hours + '时' : '') +
    (minutes > 0 ? minutes + '分' : '') +
    (seconds > 0 ? seconds + '秒' : '') +
    ((milliseconds > 0) ? milliseconds + '毫秒' : '')
  );
}
