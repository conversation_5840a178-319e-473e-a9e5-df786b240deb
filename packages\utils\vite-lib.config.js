import { defineConfig } from 'vite';
import { resolve } from 'path';
import commonjs from '@rollup/plugin-commonjs';
// https://vitejs.dev/config/

export default defineConfig({
  plugins: [
    //解决引入commonjs模块后打包出现的{'default' is not exported by XXX}错误!!
    commonjs({
      requireReturnsDefault: true,
    }),
  ],

  resolve: {
    alias: {
      // '@build': resolve(__dirname, './../../build'),
      // '/#': resolve(__dirname, './types'),
      // "/@": resolve(__dirname, 'src'), // 路径别名
    },
    extensions: ['.js', '.vue', '.json', '.ts'], // 使用路径别名时想要省略的后缀名，可以自己 增减
  },

  build: {
    //minify: false,
    lib: {
      entry: resolve('./index.ts'),
      name: 'utils',
      fileName: (format) => `utils.${format}.js`,
    },
    rollupOptions: {
      // 确保外部化处理那些你不想打包进库的依赖
      external: [],
      output: {},
    },
  },
});
