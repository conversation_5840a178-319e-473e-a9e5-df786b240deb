// token key
export const TOKEN_KEY = 'TOKEN__';
export const PERMISSON_CODE_KEY = 'PERMISSON__CODE__';
// base global local key
export const APP_LOCAL_CACHE_KEY = 'COMMON__LOCAL__KEY__';
// user info key
export const USER_INFO_KEY = 'USER__INFO__';
// permission key
export const PERM_CODE_KEY = 'PERM__CODE__';
// menu key
export const MENU_KEY = 'MENU__LIST__';
// current menu
export const CUR_MENU_KEY = 'CUR__MENU__';
// header select tenant project key
export const HEADER_TENANT_PROJECT_KEY = 'HEADER_TENANT_PROJECT_';
// header select tenant project key
export const DICT_KEY = 'USER_DICT_';
// responsive-storage namespace
export const RESPONSIVE_STORAGE_NAMESPACE = 'PROJECT__SETTINGS__';

export const PROJECT_SETTINGS = 'PROJECT__SETTINGS__';

export enum CacheTypeEnum {
  SESSION,
  LOCAL,
}
