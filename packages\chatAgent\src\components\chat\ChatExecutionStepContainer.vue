<!--
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
-->
<template>
  <div class="execution-step-container">
    <!-- 智能体头部 -->
    <div class="agent-header" v-if="stepData.agentName">
      <div class="agent-info">
        <Icon icon="carbon:bot" class="agent-icon" />
        <span class="agent-name">{{ stepData.agentName }}</span>
        <span class="agent-status" :class="getAgentStatusClass(stepData.status)">
          {{ getAgentStatusText(stepData.status) }}
        </span>
      </div>
    </div>

    <!-- 执行步骤内容 -->
    <div class="step-content">
      <!-- 思考输出 -->
      <div v-if="stepData.thinkOutput" class="think-section">
        <div class="section-header">
          <Icon icon="carbon:idea" />
          <span>思考过程</span>
        </div>
        <div class="think-content">
          {{ stepData.thinkOutput }}
        </div>
      </div>

      <!-- 工具调用 -->
      <div v-if="stepData.actionNeeded && stepData.toolName" class="tool-section">
        <div class="section-header">
          <Icon icon="carbon:tool-box" />
          <span>调用工具: {{ stepData.toolName }}</span>
          <button 
            class="detail-btn" 
            @click="toggleDetails"
            :title="showDetails ? '隐藏详情' : '查看详情'"
          >
            <Icon :icon="showDetails ? 'carbon:chevron-up' : 'carbon:chevron-down'" />
            <span>{{ stepData.id }}</span>
          </button>
        </div>

        <!-- 工具详情（可展开） -->
        <div v-if="showDetails" class="tool-details">
          <div v-if="stepData.toolInfo" class="tool-info">
            <div class="detail-section">
              <h4>工具参数</h4>
              <pre class="json-content">{{ formatJson(stepData.toolInfo.parameters) }}</pre>
            </div>
            
            <div v-if="stepData.toolInfo.result" class="detail-section">
              <h4>执行结果</h4>
              <pre class="json-content">{{ formatJson(stepData.toolInfo.result) }}</pre>
            </div>
          </div>
        </div>

        <!-- 工具状态指示器 -->
        <div class="tool-status">
          <div class="status-indicator" :class="getToolStatusClass(stepData.status)">
            <Icon :icon="getToolStatusIcon(stepData.status)" />
            <span>{{ getToolStatusText(stepData.status) }}</span>
          </div>
          
          <div v-if="stepData.thinkStartTime" class="time-info">
            <span>开始时间: {{ formatTime(stepData.thinkStartTime) }}</span>
            <span v-if="stepData.actEndTime">
              | 结束时间: {{ formatTime(stepData.actEndTime) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="stepData.errorInfo" class="error-section">
        <div class="section-header error">
          <Icon icon="carbon:warning" />
          <span>执行错误</span>
        </div>
        <div class="error-content">
          {{ stepData.errorInfo }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Icon } from '@iconify/vue'
import dayjs from 'dayjs'

interface ExecutionStepData {
  id: string
  thinkOutput?: string
  actionNeeded: boolean
  toolName?: string
  status: string
  thinkStartTime?: string
  actEndTime?: string
  agentExecutionId?: number
  agentName?: string
  toolInfo?: {
    parameters?: any
    result?: any
  }
  errorInfo?: string
  timestamp: Date
}

defineProps<{
  stepData: ExecutionStepData
}>()

const showDetails = ref(false)

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const getAgentStatusClass = (status: string) => {
  const statusMap = {
    'RUNNING': 'status-running',
    'COMPLETED': 'status-completed',
    'FAILED': 'status-failed',
    'PENDING': 'status-pending'
  }
  return statusMap[status] || 'status-unknown'
}

const getAgentStatusText = (status: string) => {
  const statusMap = {
    'RUNNING': '执行中',
    'COMPLETED': '已完成',
    'FAILED': '执行失败',
    'PENDING': '等待中'
  }
  return statusMap[status] || status
}

const getToolStatusClass = (status: string) => {
  return getAgentStatusClass(status)
}

const getToolStatusIcon = (status: string) => {
  const iconMap = {
    'RUNNING': 'carbon:circle-dash',
    'COMPLETED': 'carbon:checkmark-filled',
    'FAILED': 'carbon:error-filled',
    'PENDING': 'carbon:time'
  }
  return iconMap[status] || 'carbon:help'
}

const getToolStatusText = (status: string) => {
  return getAgentStatusText(status)
}

const formatTime = (timeStr: string) => {
  return dayjs(timeStr).format('HH:mm:ss')
}

const formatJson = (obj: any) => {
  if (!obj) return ''
  try {
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return String(obj)
  }
}
</script>

<style lang="less" scoped>
.execution-step-container {
  margin: 12px 0;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  overflow: hidden;
  animation: stepSlideIn 0.3s ease;
}

@keyframes stepSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.agent-header {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  padding: 10px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.agent-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.agent-icon {
  font-size: 1.1rem;
}

.agent-name {
  font-weight: 500;
  flex: 1;
}

.agent-status {
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.2);
  
  &.status-running {
    background: rgba(251, 191, 36, 0.8);
  }
  
  &.status-completed {
    background: rgba(16, 185, 129, 0.8);
  }
  
  &.status-failed {
    background: rgba(239, 68, 68, 0.8);
  }
  
  &.status-pending {
    background: rgba(107, 114, 128, 0.8);
  }
}

.step-content {
  padding: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1f2937;
  
  &.error {
    color: #dc2626;
  }
}

.detail-btn {
  margin-left: auto;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 4px 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: #6b7280;
  transition: all 0.2s ease;
  
  &:hover {
    background: #e5e7eb;
    color: #374151;
  }
}

.think-section {
  margin-bottom: 16px;
}

.think-content {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  font-size: 0.875rem;
  line-height: 1.5;
  white-space: pre-wrap;
  color: #374151;
}

.tool-section {
  margin-bottom: 16px;
}

.tool-details {
  margin: 12px 0;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #f9fafb;
}

.detail-section {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  
  &:last-child {
    border-bottom: none;
  }
  
  h4 {
    margin: 0 0 8px 0;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }
}

.json-content {
  background: #1f2937;
  color: #f9fafb;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  overflow-x: auto;
  margin: 0;
  white-space: pre-wrap;
}

.tool-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
  font-size: 0.75rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  
  &.status-running {
    color: #f59e0b;
  }
  
  &.status-completed {
    color: #10b981;
  }
  
  &.status-failed {
    color: #ef4444;
  }
  
  &.status-pending {
    color: #6b7280;
  }
}

.time-info {
  color: #6b7280;
  
  span {
    margin-right: 8px;
  }
}

.error-section {
  margin-bottom: 16px;
}

.error-content {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  font-size: 0.875rem;
  color: #dc2626;
}
</style>
