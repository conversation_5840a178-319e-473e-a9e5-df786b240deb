export async function downBpmXML(bpmnModeler) {

  try {
    const result = await bpmnModeler.saveXML({ format: true });
    const { xml } = result;
    // debugger
    downloadFile(`${getProcessElement(bpmnModeler).id}.bpmn`, xml, 'application/xml');
  } catch (err) {
    console.log(err);
  }
}
export async function downDmnXML(dmnModeler) {
  try {
    const result = await dmnModeler.saveXML({ format: true });
    // debugger
    const { xml } = result;
    downloadFile(
      `${getDmnProcessElement(dmnModeler).name}.dmn`,
      xml,
      'application/xml',
    );
  } catch (err) {
    console.log(err);
  }
}
// 获取文件名称
export function getDmnProcessElement(dmnModeler?) {
  const rootElements = dmnModeler.getDefinitions();
  // debugger
  return rootElements || 'dish';
}

// 下载方法
export function downloadFile(filename, data, type) {
  const a = document.createElement('a');
  const url = window.URL.createObjectURL(new Blob([data], { type: type }));
  a.href = url;
  a.download = filename;
  a.click();
  window.URL.revokeObjectURL(url);
}

// 获取文件名称
export function getProcessElement(bpmnModeler?: any) {
  // let processKey = 'Process_' + new Date().getTime()
  const rootElements = bpmnModeler?.getDefinitions().rootElements;

  // debugger;
  for (let i = 0; i < rootElements.length; i++) {
    // if (rootElements[i].$type === 'bpmn:Process')  return rootElements[i];
    if (rootElements[i].$type === 'bpmn:Process') {
      return rootElements[i];
    }
  }
}
