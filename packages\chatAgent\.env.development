# 开发环境
NODE_ENV=development

VITE_PORT=5173

# 接口基础请求地址  通常配置如：http://localhost:9000
VITE_API_BASE_URL=
# 与VITE_APP_BASE_URL配合 通常配置 如： /api => http://localhost:9000/api
# VITE_APP_BASE_URL_PREFIX=/basic-api
VITE_APP_BASE_URL_PREFIX=
# 代理
VITE_PROXY = [["/sys","http://************:18081"],["/cpit","http://************:18081/cpit"],["/api","http://************:18081/api"]]
# 静态资源地址()
VITE_APP_STATIC_URL=

# 构建资源公共路径
VITE_PUBLIC_PATH=/
