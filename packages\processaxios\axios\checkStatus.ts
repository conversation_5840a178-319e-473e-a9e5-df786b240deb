/*
 * @Author: wangneng <EMAIL>
 * @Date: 2022-08-01 14:30:52
 * @LastEditors: wangneng <EMAIL>
 * @LastEditTime: 2022-08-01 15:01:40
 * @FilePath: \cpvf-pools\src\utils\axios\checkStatus.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import type { ErrorMessageMode } from './../types/axios';
// import { useMessage } from './hooks/web/useMessage';
// import { useI18n } from './hooks/web/useI18n';
// import t from './lang/zh-CN/sys';
// import { transformI18n } from '/@/utils/i18n';
// import { useUserStoreWithOut } from '/@/stores/modules/user';
import { useMessage } from './../hooks/web/useMesage';
// import router from '/@/router';
// import { PageEnum } from '/@/enums/pageEnum';
// import projectSetting from '/@/settings/projectSetting';
// import { SessionTimeoutProcessingEnum } from './enums/appEnum';
import { api } from './lang/zh-CN/sys'
const { createMessage } = useMessage();
// const stp = projectSetting.sessionTimeoutProcessing;
let abnormalFlag = false;
export function checkStatus(
  status: number,
  msg: string,
  errorMessageMode: ErrorMessageMode = 'message',
): void {
  // const { t } = useI18n();
  // const userStore = useUserStoreWithOut();
  let errMessage = '';
  errMessage = `${msg}`;
  switch (status) {
    case 400:
      errMessage = api.apiRequestFailed;
      break;
  //   // 401: Not logged in
  //   // Jump to the login page if not logged in, and carry the path of the current page
  //   // Return to the current page after successful login. This step needs to be operated on the login page.
    case 401:
      window.localStorage.clear()
      window.location.replace(`${window.location.origin}/#/login`)
  //     // errMessage = transformI18n('api.errMsg401', true);
  //     // userStore.setToken(undefined);
  //     // errMessage = transformI18n('api.errMsg401', true) || msg;
  //     // userStore.logout(401);
  //     console.log(401);

      break;
    case 403:
      errMessage = api.errMsg403;
      // errMessage = transformI18n('api.errMsg403', true);
      break;
  //   // 404请求不存在
    case 404:
      errMessage = api.errMsg404;
      break;
    case 405:
      errMessage = api.errMsg405;
      // errMessage = transformI18n('api.errMsg405', true);
      break;
    case 408:
      errMessage = api.errMsg408;
      // errMessage = transformI18n('api.errMsg408', true);
      break;
    case 500:
      errMessage = api.errMsg500;
      // errMessage = transformI18n('api.errMsg500', true);
      break;
    case 501:
      errMessage = api.errMsg501;
      // errMessage = transformI18n('api.errMsg501', true);
      break;
    case 502:
      errMessage = api.errMsg502;
      // errMessage = transformI18n('api.errMsg502', true);
      break;
    case 503:
      errMessage = api.errMsg503;
      // errMessage = transformI18n('api.errMsg503', true);
      break;
    case 504:
      errMessage = api.errMsg504;
      // errMessage = transformI18n('api.errMsg504', true);
      break;
    case 505:
      errMessage = api.errMsg505;
      // errMessage = transformI18n('api.errMsg505', true);
      break;
    default:
      errMessage = "未知错误"
  }

  errMessage = msg || errMessage;

  if (errMessage&&!abnormalFlag) {
    if (errorMessageMode === 'modal') {
      // createErrorModal({ title: transformI18n('api.errorTip', true), content: errMessage });
    } 
    // else if (errorMessageMode === 'message') {
    //   abnormalFlag = false
    //   createMessage.error(errMessage);
      
    // } 
    else {
      abnormalFlag = true
      createMessage.error(errMessage);
      setTimeout(() => {
        abnormalFlag = false;
      }, 3000);
    }
  }
}
