declare module '@/views/chatagent/api/axios-api-service' {
  export interface DataSource {
    id: number;
    name: string;
    type: string;
    host?: string;
    port?: number;
    description?: string;
    supportDatabase?: boolean;
    supportSchema?: boolean;
  }

  export interface Database {
    name: string;
    description?: string;
    count?: number;
  }

  export interface Schema {
    name: string;
  }

  export class AxiosApiService {
    static getDataSourceList(options?: any): Promise<DataSource[]>;
    static getDatabaseList(dataSourceId: number, options?: any): Promise<Database[]>;
    static getSchemaList(dataSourceId: number, databaseName: string, options?: any): Promise<Schema[]>;
  }
} 