{"name": "chat-ai-ui", "version": "1.0.0", "description": "基于Vue3和ElementPlus的AI对话界面组件", "main": "dist/chat-ai-ui.umd.js", "module": "dist/chat-ai-ui.es.js", "types": "dist/types/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite", "build:lib": "vite build --mode lib", "build:app": "vite build", "build": "npm run build:lib && npm run build:app", "preview": "vite preview"}, "keywords": ["vue3", "element-plus", "chat", "ai"], "author": "", "license": "MIT", "dependencies": {"@element-plus/icons-vue": "^2.3.1", "vue": "^3.3.4", "@iconify-json/carbon": "^1.2.10", "@iconify/vue": "^4.1.1", "@interactjs/actions": "^1.10.27", "@interactjs/auto-scroll": "^1.10.27", "@interactjs/auto-start": "^1.10.27", "@interactjs/core": "^1.10.27", "@interactjs/dev-tools": "^1.10.27", "@interactjs/interact": "^1.10.27", "@interactjs/modifiers": "^1.10.27", "@visactor/vtable": "^1.19.1", "@vueuse/core": "11.0.3", "@vueuse/motion": "2.2.6", "@wangeditor/editor-for-vue": "^5.1.12", "autoprefixer": "10.4.20", "axios": "1.6.0", "crypto-js": "4.1.1", "css-color-function": "1.3.3", "current-device": "0.10.2", "dayjs": "1.11.13", "defu": "6.1.4", "echarts": "^5.6.0", "element-plus": "2.9.10", "element-resize-detector": "1.2.4", "highlight.js": "11.11.1", "interact": "^0.0.3", "interactjs": "^1.10.27", "jquery": "3.7.0", "json-editor-vue": "0.14.0", "localforage": "1.10.0", "lodash": "4.17.21", "lodash-es": "4.17.21", "lodash-unified": "1.0.3", "marked": "^16.0.0", "marked-highlight": "2.2.2", "mitt": "3.0.1", "mockjs": "1.1.0", "monaco-editor": "^0.52.2", "normalize.css": "8.0.1", "nprogress": "0.2.0", "pinia": "2.2.2", "postcss": "8.4.45", "postcss-import": "16.1.0", "qrcode.vue": "3.4.1", "qs": "6.13.0", "resize-observer-polyfill": "1.5.1", "responsive-storage": "2.2.0", "rgb-hex": "4.1.0", "screenfull": "6.0.2", "sm-crypto": "^0.3.13", "sortablejs": "1.15.3", "splitpanes": "3.1.5", "store": "2.0.12", "ua-parser-js": "1.0.39", "@chat-agent/utils": "workspace:1.0.0", "@chat-agent/axios": "workspace:1.0.0"}, "devDependencies": {"@types/node": "^20.4.5", "@vitejs/plugin-vue": "^4.2.3", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.70.0", "typescript": "^5.1.6", "vite": "^4.4.7", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^1.8.8"}, "peerDependencies": {"element-plus": "^2.3.0", "vue": "^3.3.0"}}