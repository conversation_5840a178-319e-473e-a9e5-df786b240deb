import { formTableQuery } from './vxecommon/formTableQuery';
import { gridTableConfig, proxyProps } from './vxecommon/vxetableconfig';
import { downBpmXML, downDmnXML, downloadFile, getProcessElement } from './xmldown';
import { intervalTime } from './time';
import { getDictStorage } from './dict'; //缓存获取字典值
import { getAuthStorage, getPermissonCode } from './auth';
import { formatFormData } from './form/index';
import { getAuthToken, hasPerm } from './auth';
import { formatterType } from './fomaterType';
export {
  formTableQuery,
  gridTableConfig,
  proxyProps,
  downBpmXML,
  downDmnXML,
  downloadFile,
  getProcessElement,
  intervalTime,
  getDictStorage,
  getAuthStorage,
  formatFormData,
  getPermissonCode,
  getAuthToken,
  formatterType,
  hasPerm,
};
