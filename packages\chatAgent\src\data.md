data:{"type":"THINK_CHUNK","planId":"chat-1750234823783-5mywvgjh","payload":"","timestamp":"2025-06-18T16:20:24.0695753","compressed":false,"entityType":"thinkAct","entityId":"system","updateType":"chunk"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"{\"chatId\":\"chat-1750234823783-5mywvgjh\",\"executions\":[{\"planId\":\"planTemplate-1749011464049\",\"chatId\":\"chat-1750234823783-5mywvgjh\",\"title\":\"Plan for: 根据用户输入使用大模型进行自然语言分析，生成sql并执行查询\",\"userRequest\":\"y\",\"startTime\":\"2025-06-18T16:20:24.0682797\",\"endTime\":null,\"completed\":false,\"summary\":null,\"steps\":[],\"currentStepIndex\":null,\"formattedStartTime\":\"2025-06-18 16:20:24\",\"agentExecutionSequence\":[]}]}","timestamp":"2025-06-18T16:20:24.0709772","compressed":false,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"{\"chatId\":\"chat-1750234823783-5mywvgjh\",\"executions\":[{\"planId\":\"planTemplate-1749011464049\",\"chatId\":\"chat-1750234823783-5mywvgjh\",\"title\":\"Plan for: 根据用户输入使用大模型进行自然语言分析，生成sql并执行查询\",\"userRequest\":\"y\",\"startTime\":\"2025-06-18T16:20:24.0682797\",\"endTime\":null,\"completed\":false,\"summary\":null,\"steps\":[],\"currentStepIndex\":null,\"formattedStartTime\":\"2025-06-18 16:20:24\",\"agentExecutionSequence\":[]}]}","timestamp":"2025-06-18T16:20:24.0723972","compressed":false,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"{\"chatId\":\"chat-1750234823783-5mywvgjh\",\"executions\":[{\"planId\":\"planTemplate-1749011464049\",\"chatId\":\"chat-1750234823783-5mywvgjh\",\"title\":\"根据用户输入使用大模型进行自然语言分析，生成sql并执行查询\",\"userRequest\":\"y\",\"startTime\":\"2025-06-18T16:20:24.0723972\",\"endTime\":null,\"completed\":false,\"summary\":null,\"steps\":[\"0. [not_started] [SQL_GENERATOR_AGENT] 生成准确的PostgreSQL查询语句\",\"1. [not_started] [SQL_EXECUTION_INTERPRETER_AGENT] 执行SQL并处理结果\"],\"currentStepIndex\":null,\"formattedStartTime\":\"2025-06-18 16:20:24\",\"agentExecutionSequence\":[]}]}","timestamp":"2025-06-18T16:20:24.0723972","compressed":false,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"{\"chatId\":\"chat-1750234823783-5mywvgjh\",\"executions\":[{\"planId\":\"planTemplate-1749011464049\",\"chatId\":\"chat-1750234823783-5mywvgjh\",\"title\":\"根据用户输入使用大模型进行自然语言分析，生成sql并执行查询\",\"userRequest\":\"y\",\"startTime\":\"2025-06-18T16:20:24.0723972\",\"endTime\":null,\"completed\":false,\"summary\":null,\"steps\":[\"0. [not_started] [SQL_GENERATOR_AGENT] 生成准确的PostgreSQL查询语句\",\"1. [not_started] [SQL_EXECUTION_INTERPRETER_AGENT] 执行SQL并处理结果\"],\"currentStepIndex\":null,\"formattedStartTime\":\"2025-06-18 16:20:24\",\"agentExecutionSequence\":[]}]}","timestamp":"2025-06-18T16:20:24.4419056","compressed":false,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"{\"chatId\":\"chat-1750234823783-5mywvgjh\",\"executions\":[{\"planId\":\"planTemplate-1749011464049\",\"chatId\":\"chat-1750234823783-5mywvgjh\",\"title\":\"根据用户输入使用大模型进行自然语言分析，生成sql并执行查询\",\"userRequest\":\"y\",\"startTime\":\"2025-06-18T16:20:24.0723972\",\"endTime\":null,\"completed\":false,\"summary\":null,\"steps\":[\"0. [in_progress] [SQL_GENERATOR_AGENT] 生成准确的PostgreSQL查询语句\",\"1. [not_started] [SQL_EXECUTION_INTERPRETER_AGENT] 执行SQL并处理结果\"],\"currentStepIndex\":0,\"formattedStartTime\":\"2025-06-18 16:20:24\",\"agentExecutionSequence\":[]}]}","timestamp":"2025-06-18T16:20:24.4433041","compressed":false,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"AGENT_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"{\"id\":\"27\",\"conversationId\":\"planTemplate-1749011464049\",\"agentName\":\"SQL_GENERATOR_AGENT\",\"agentDescription\":\"深入理解用户的自然语言请求，通过调用工具获取并分析必要的数据库上下文，生成一条准确且符合规范的 PostgreSQL 查询语句\",\"startTime\":\"2025-06-18T16:20:24.443304100\",\"maxSteps\":5,\"currentStep\":0,\"status\":\"in_progress\",\"isCompleted\":false,\"isStuck\":false}","timestamp":"2025-06-18T16:20:24.4433041","compressed":false,"entityType":"agent","entityId":"27","updateType":"full"}

data:{"type":"PLAN_INCREMENTAL","planId":"chat-1750234823783-5mywvgjh","payload":{"currentStepIndex":0,"completed":false},"timestamp":"2025-06-18T16:20:24.4433041","compressed":false,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"incremental"}

data:{"type":"THINK_COMPLETE","planId":"chat-1750234823783-5mywvgjh","payload":"{\"parentExecutionId\":\"27\",\"thinkStartTime\":\"2025-06-18T16:20:24.479465500\",\"thinkEndTime\":\"2025-06-18T16:20:25.952688200\",\"actStartTime\":\"2025-06-18T16:20:24.445313600\",\"thinkInput\":\"- SYSTEM INFORMATION:\\r\\nOS: Windows 11 10.0 (amd64)\\r\\n\\r\\n- Current Date:\\r\\n2025-06-18\\r\\n- 全局计划信息:\\r\\n\\r\\n- 执行参数: \\r\\n未提供执行参数。\\r\\n\\r\\n- 全局步骤计划:\\r\\n\\r\\n\\r\\n- 当前要做的步骤要求(这个步骤是需要当前智能体完成的!) :\\r\\nSTEP 0 :[SQL_GENERATOR_AGENT] y\\r\\n\\r\\n- 当前步骤的上下文信息:\\r\\n\\r\\n重要说明：\\r\\n1. 使用工具调用时，不需要额外的任何解释说明！\\r\\n2. 不要在工具调用前提供推理或描述！\\r\\n\\r\\n3. 做且只做当前要做的步骤要求中的内容\\r\\n4. 如果当前要做的步骤要求已经做完，则调用terminate工具来完成当前步骤。\\r\\n5. 全局目标 是用来有个全局认识的，不要在当前步骤中去完成这个全局目标。\\r\\n\\r\\n\\n\\r\\n当前步骤的环境信息是:\\r\\nfiltered_search 的上下文信息：\\r\\n    过滤查询工具, 根据指定类型执行向量搜索，例如 documentType == 'table_schema'\\r\\ncombined_search 的上下文信息：\\r\\n    组合搜索工具\\r\\nterminate 的上下文信息：\\r\\n    Termination Tool Status:\\r\\n- Current State: ⚡ Active\\r\\n- Last Termination: No termination recorded\\r\\n- Termination Message: N/A\\r\\n- Timestamp: N/A\\r\\n\\r\\n\\r\\n\\r\\n\\n# **你是一位专业的 PostgreSQL 查询分析专家** \uD83E\uDDD0\\r\\n\\r\\n你的目标是：理解用户自然语言，通过调用工具获取数据库上下文（如表结构、样本数据、SQL案例等），**最终生成准确的 PostgreSQL 查询语句**，以供后续执行。\\r\\n\\r\\n## **核心原则** \uD83D\uDCA1\\r\\n\\r\\n### 1\\\\. **信息获取与评估**\\r\\n* **上下文优先**：必须先充分评估和利用当前已有的对话历史和上下文信息。\\r\\n* **工具调用策略**：\\r\\n  * **`combinedSearch`**：仅当现有上下文完全无法提供理解用户请求所需的基础信息时使用。\\r\\n  * **`filteredSearch`**：当需要特定信息（表结构、样本数据、SQL案例）且现有上下文缺失此信息时使用。\\r\\n* **避免冗余**：获取到足够信息后，不重复调用工具。\\r\\n\\r\\n### 2\\\\. **查询意图理解**\\r\\n* **追问处理**：识别追问，强制回顾并利用前序对话，继承并合并筛选条件与上下文。\\r\\n* **核心要素识别**：明确查询类型、指标、维度、筛选条件、时间、操作。\\r\\n* **语义映射**：将用户表述映射到数据库字段和值，冲突时优先采用表结构信息。\\r\\n* **实体值匹配**：优先使用工具返回的完整/官方值进行精确匹配，仅在必要时使用模糊匹配。\\r\\n\\r\\n### 3\\\\. **SQL构建**\\r\\n* **规范**：必须有`WHERE`（极少数情况除外），禁止`SELECT *`，SQL中不用换行。\\r\\n* **参考**：参考工具返回的SQL案例中相似案例的结构与逻辑。\\r\\n* **前提**：仅当所有必要信息都已充分，且用户意图清晰时，才生成SQL。\\r\\n\\r\\n### 4\\\\. **自我验证**\\r\\n* 生成SQL后，检查：\\r\\n  * SQL语法是否正确？\\r\\n  * 是否满足用户所有查询条件？\\r\\n  * 表和字段名称是否与数据库结构匹配？\\r\\n  * 查询逻辑是否符合用户意图？\\r\\n\\r\\n---\\r\\n# **工具使用决策树** \uD83D\uDD0D\\r\\n\\r\\n1. **是否有足够信息生成SQL？**\\r\\n   * 是 → 直接生成SQL并终止\\r\\n   * 否 → 继续评估\\r\\n\\r\\n2. **是否知道相关表但缺少表结构？**\\r\\n   * 是 → `filteredSearch`(表名, documentType='table_schema')\\r\\n   * 否 → 继续评估\\r\\n\\r\\n3. **是否需要样本数据或SQL案例？**\\r\\n   * 是 → `filteredSearch`(相关关键词, documentType='sampleData'/'sql_case')\\r\\n   * 否 → 继续评估\\r\\n\\r\\n4. **是否完全缺乏领域背景？**\\r\\n   * 是 → `combinedSearch`(用户问题)\\r\\n   * 否 → 向用户澄清（提供选项）\\r\\n\\r\\n# **工具使用说明** \uD83D\uDEE0️\\r\\n\\r\\n* **`combinedSearch`**：获取全面初始上下文。`query`使用用户自然语言。\\r\\n* **`filteredSearch`**：针对性补充特定信息。\\r\\n  * `filterExpression`中的`documentType`必须为`'table_schema'`, `'sampleData'`, 或`'sql_case'`三者之一。\\r\\n  * 当`documentType == 'table_schema'`时，`query`应为已识别的表名。\\r\\n* **`terminate`**：成功生成SQL后调用，将SQL语句作为`reason`参数的值。\\r\\n\\r\\n# **输出格式** \uD83D\uDCDD\\r\\n\\r\\n## 思考过程（简洁）\\r\\n简要说明当前状态评估和决策理由（限30字内）。\\r\\n\\r\\n记住：简洁输出不等于肤浅思考。在行动前务必内部充分应用本提示词的原则与框架，优先利用现有上下文，避免不必要的工具调用。\\r\\n\\r\\n\",\"thinkOutput\":\"\",\"actionNeeded\":false}","timestamp":"2025-06-18T16:20:25.9526882","compressed":false,"entityType":"thinkAct","entityId":"27","updateType":"complete"}

data:{"type":"AGENT_INCREMENTAL","planId":"chat-1750234823783-5mywvgjh","payload":{"currentStep":1,"isStuck":false,"status":"in_progress","isCompleted":false},"timestamp":"2025-06-18T16:20:25.9547502","compressed":false,"entityType":"agent","entityId":"27","updateType":"incremental"}

data:{"type":"TOOL_START","planId":"chat-1750234823783-5mywvgjh","payload":{"parameters":"{\"query\": \"y\"}","toolName":"combined_search","timestamp":"2025-06-18T16:20:25.959640700"},"timestamp":"2025-06-18T16:20:25.9596407","compressed":false,"entityType":"tool","entityId":"27","updateType":"start"}

data:{"type":"TOOL_END","planId":"chat-1750234823783-5mywvgjh","payload":{"result":"{\"output\":\"{\\\"retrievedKnowledge\\\":{\\\"tableSchemas\\\":[{\\\"tableType\\\":\\\"TABLE\\\",\\\"relevanceScore\\\":0.17128616571426392,\\\"distance\\\":0.82871383,\\\"documentType\\\":\\\"table_schema\\\",\\\"columns\\\":[{\\\"dataType\\\":\\\"varchar\\\",\\\"name\\\":\\\"unseal_city_code\\\",\\\"description\\\":\\\"卸交站所属地市代码\\\"},{\\\"dataType\\\":\\\"varchar\\\",\\\"name\\\":\\\"base_product_code\\\",\\\"description\\\":\\\"基础产品代码：国内即日(11111),国内特快专递(11210),国内快递包裹(11312),国内电商标快(11510),国内标准快递(11610)\\\"},{\\\"dataType\\\":\\\"varchar\\\",\\\"name\\\":\\\"statistics_date\\\",\\\"description\\\":\\\"统计时间：格式yyyy-mm-dd hh:mm:ss 例:2025-03-18 08:00:00\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"overrated_num1\\\",\\\"description\\\":\\\"邮件状态-已逾限24小时以上邮件数\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"overrated_num2\\\",\\\"description\\\":\\\"邮件状态-已逾限12~24小时邮件数\\\"},{\\\"dataType\\\":\\\"varchar\\\",\\\"name\\\":\\\"product_code\\\",\\\"description\\\":\\\"可售卖产品代码\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"overrated_num3\\\",\\\"description\\\":\\\"邮件状态-已逾限6~12小时邮件数\\\"},{\\\"dataType\\\":\\\"timestamp\\\",\\\"name\\\":\\\"gmt_modified\\\",\\\"description\\\":\\\"修改时间\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"reserved_fields1\\\",\\\"description\\\":\\\"固定值0\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"overrated_num4\\\",\\\"description\\\":\\\"邮件状态-已逾限6小时以内邮件数\\\"},{\\\"dataType\\\":\\\"varchar\\\",\\\"name\\\":\\\"mail_state_flag\\\",\\\"description\\\":\\\"邮件状态：已解车未扫描(TCJF)，已扫描未配发(TCFF)，已配发未封车(TCPF)\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"overrated_num5\\\",\\\"description\\\":\\\"邮件状态-已逾限0-2小时邮件数\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"overrated_num6\\\",\\\"description\\\":\\\"邮件状态-已逾限2-4小时邮件数\\\"},{\\\"dataType\\\":\\\"varchar\\\",\\\"name\\\":\\\"is_deleted\\\",\\\"description\\\":\\\"是否删除：正常(0),已删除(1)\\\"},{\\\"dataType\\\":\\\"varchar\\\",\\\"name\\\":\\\"unseal_org_code\\\",\\\"description\\\":\\\"卸交站代码\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"overrated_num10\\\",\\\"description\\\":\\\"邮件状态-已逾限24-48小时邮件数\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"overrated_num11\\\",\\\"description\\\":\\\"邮件状态-已逾限48-72小时邮件数\\\"},{\\\"dataType\\\":\\\"int4\\\",\\\"name\\\":\\\"overrated_num12\\\",\\\"description\\\":\\\"邮件状态-已逾限72小时以上小时邮件数\\\"},{\\\"dataType\\\":\\\"varchar\\\",\\\"name\\\":\\\"tean_flag\\\",\\\"description\\\":\\\"特殊标识：特安标识(1)\\\"},{\\\"dataType\\\":\\\"i\n\n[输出已截断] 工具 'combined_search' 的输出过长（14014 字符），已截断至 2000 字符。完整输出请查看日志。","parameters":null,"toolName":"combined_search","timestamp":"2025-06-18T16:20:26.476343800"},"timestamp":"2025-06-18T16:20:26.4763438","compressed":false,"entityType":"tool","entityId":"27","updateType":"end"}

data:{"type":"THINK_COMPLETE","planId":"chat-1750234823783-5mywvgjh","payload":"{\"parentExecutionId\":\"27\",\"thinkStartTime\":\"2025-06-18T16:20:26.487342600\",\"thinkEndTime\":\"2025-06-18T16:20:28.541209\",\"actStartTime\":\"2025-06-18T16:20:26.479342200\",\"thinkInput\":\"- SYSTEM INFORMATION:\\r\\nOS: Windows 11 10.0 (amd64)\\r\\n\\r\\n- Current Date:\\r\\n2025-06-18\\r\\n- 全局计划信息:\\r\\n\\r\\n- 执行参数: \\r\\n未提供执行参数。\\r\\n\\r\\n- 全局步骤计划:\\r\\n\\r\\n\\r\\n- 当前要做的步骤要求(这个步骤是需要当前智能体完成的!) :\\r\\nSTEP 0 :[SQL_GENERATOR_AGENT] y\\r\\n\\r\\n- 当前步骤的上下文信息:\\r\\n\\r\\n重要说明：\\r\\n1. 使用工具调用时，不需要额外的任何解释说明！\\r\\n2. 不要在工具调用前提供推理或描述！\\r\\n\\r\\n3. 做且只做当前要做的步骤要求中的内容\\r\\n4. 如果当前要做的步骤要求已经做完，则调用terminate工具来完成当前步骤。\\r\\n5. 全局目标 是用来有个全局认识的，不要在当前步骤中去完成这个全局目标。\\r\\n\\r\\n\\n\\r\\n当前步骤的环境信息是:\\r\\nfiltered_search 的上下文信息：\\r\\n    过滤查询工具, 根据指定类型执行向量搜索，例如 documentType == 'table_schema'\\r\\ncombined_search 的上下文信息：\\r\\n    组合搜索工具\\r\\nterminate 的上下文信息：\\r\\n    Termination Tool Status:\\r\\n- Current State: ⚡ Active\\r\\n- Last Termination: No termination recorded\\r\\n- Termination Message: N/A\\r\\n- Timestamp: N/A\\r\\n\\r\\n\\r\\n\\r\\n\\n# **你是一位专业的 PostgreSQL 查询分析专家** \uD83E\uDDD0\\r\\n\\r\\n你的目标是：理解用户自然语言，通过调用工具获取数据库上下文（如表结构、样本数据、SQL案例等），**最终生成准确的 PostgreSQL 查询语句**，以供后续执行。\\r\\n\\r\\n## **核心原则** \uD83D\uDCA1\\r\\n\\r\\n### 1\\\\. **信息获取与评估**\\r\\n* **上下文优先**：必须先充分评估和利用当前已有的对话历史和上下文信息。\\r\\n* **工具调用策略**：\\r\\n  * **`combinedSearch`**：仅当现有上下文完全无法提供理解用户请求所需的基础信息时使用。\\r\\n  * **`filteredSearch`**：当需要特定信息（表结构、样本数据、SQL案例）且现有上下文缺失此信息时使用。\\r\\n* **避免冗余**：获取到足够信息后，不重复调用工具。\\r\\n\\r\\n### 2\\\\. **查询意图理解**\\r\\n* **追问处理**：识别追问，强制回顾并利用前序对话，继承并合并筛选条件与上下文。\\r\\n* **核心要素识别**：明确查询类型、指标、维度、筛选条件、时间、操作。\\r\\n* **语义映射**：将用户表述映射到数据库字段和值，冲突时优先采用表结构信息。\\r\\n* **实体值匹配**：优先使用工具返回的完整/官方值进行精确匹配，仅在必要时使用模糊匹配。\\r\\n\\r\\n### 3\\\\. **SQL构建**\\r\\n* **规范**：必须有`WHERE`（极少数情况除外），禁止`SELECT *`，SQL中不用换行。\\r\\n* **参考**：参考工具返回的SQL案例中相似案例的结构与逻辑。\\r\\n* **前提**：仅当所有必要信息都已充分，且用户意图清晰时，才生成SQL。\\r\\n\\r\\n### 4\\\\. **自我验证**\\r\\n* 生成SQL后，检查：\\r\\n  * SQL语法是否正确？\\r\\n  * 是否满足用户所有查询条件？\\r\\n  * 表和字段名称是否与数据库结构匹配？\\r\\n  * 查询逻辑是否符合用户意图？\\r\\n\\r\\n---\\r\\n# **工具使用决策树** \uD83D\uDD0D\\r\\n\\r\\n1. **是否有足够信息生成SQL？**\\r\\n   * 是 → 直接生成SQL并终止\\r\\n   * 否 → 继续评估\\r\\n\\r\\n2. **是否知道相关表但缺少表结构？**\\r\\n   * 是 → `filteredSearch`(表名, documentType='table_schema')\\r\\n   * 否 → 继续评估\\r\\n\\r\\n3. **是否需要样本数据或SQL案例？**\\r\\n   * 是 → `filteredSearch`(相关关键词, documentType='sampleData'/'sql_case')\\r\\n   * 否 → 继续评估\\r\\n\\r\\n4. **是否完全缺乏领域背景？**\\r\\n   * 是 → `combinedSearch`(用户问题)\\r\\n   * 否 → 向用户澄清（提供选项）\\r\\n\\r\\n# **工具使用说明** \uD83D\uDEE0️\\r\\n\\r\\n* **`combinedSearch`**：获取全面初始上下文。`query`使用用户自然语言。\\r\\n* **`filteredSearch`**：针对性补充特定信息。\\r\\n  * `filterExpression`中的`documentType`必须为`'table_schema'`, `'sampleData'`, 或`'sql_case'`三者之一。\\r\\n  * 当`documentType == 'table_schema'`时，`query`应为已识别的表名。\\r\\n* **`terminate`**：成功生成SQL后调用，将SQL语句作为`reason`参数的值。\\r\\n\\r\\n# **输出格式** \uD83D\uDCDD\\r\\n\\r\\n## 思考过程（简洁）\\r\\n简要说明当前状态评估和决策理由（限30字内）。\\r\\n\\r\\n记住：简洁输出不等于肤浅思考。在行动前务必内部充分应用本提示词的原则与框架，优先利用现有上下文，避免不必要的工具调用。\\r\\n\\r\\n\",\"thinkOutput\":\"\",\"actionNeeded\":false}","timestamp":"2025-06-18T16:20:28.5421218","compressed":false,"entityType":"thinkAct","entityId":"27","updateType":"complete"}

data:{"type":"AGENT_INCREMENTAL","planId":"chat-1750234823783-5mywvgjh","payload":{"currentStep":2,"isStuck":false,"status":"in_progress","isCompleted":false},"timestamp":"2025-06-18T16:20:28.5441228","compressed":false,"entityType":"agent","entityId":"27","updateType":"incremental"}

data:{"type":"TOOL_START","planId":"chat-1750234823783-5mywvgjh","payload":{"parameters":"{\"message\": \"无法生成有效SQL，用户查询过于模糊且未提供具体分析需求。\"}","toolName":"terminate","timestamp":"2025-06-18T16:20:28.547122900"},"timestamp":"2025-06-18T16:20:28.5471229","compressed":false,"entityType":"tool","entityId":"27","updateType":"start"}

data:{"type":"TOOL_END","planId":"chat-1750234823783-5mywvgjh","payload":{"result":"{\"output\":\"{\\\"message\\\": \\\"无法生成有效SQL，用户查询过于模糊且未提供具体分析需求。\\\"}\"}","parameters":null,"toolName":"terminate","timestamp":"2025-06-18T16:20:28.551123400"},"timestamp":"2025-06-18T16:20:28.5511234","compressed":false,"entityType":"tool","entityId":"27","updateType":"end"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"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","timestamp":"2025-06-18T16:20:28.5546019","compressed":true,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"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","timestamp":"2025-06-18T16:20:28.5606029","compressed":true,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"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","timestamp":"2025-06-18T16:20:28.6410115","compressed":true,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"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","timestamp":"2025-06-18T16:20:28.6460124","compressed":true,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"AGENT_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"{\"id\":\"28\",\"conversationId\":\"planTemplate-1749011464049\",\"agentName\":\"SQL_EXECUTION_INTERPRETER_AGENT\",\"agentDescription\":\"SQL执行与结果处理\",\"startTime\":\"2025-06-18T16:20:28.650014600\",\"maxSteps\":5,\"currentStep\":0,\"status\":\"in_progress\",\"isCompleted\":false,\"isStuck\":false}","timestamp":"2025-06-18T16:20:28.6520488","compressed":false,"entityType":"agent","entityId":"28","updateType":"full"}

data:{"type":"PLAN_INCREMENTAL","planId":"chat-1750234823783-5mywvgjh","payload":{"currentStepIndex":1,"completed":false},"timestamp":"2025-06-18T16:20:28.6550145","compressed":false,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"incremental"}

data:{"type":"THINK_COMPLETE","planId":"chat-1750234823783-5mywvgjh","payload":"{\"parentExecutionId\":\"28\",\"thinkStartTime\":\"2025-06-18T16:20:28.658015\",\"thinkEndTime\":\"2025-06-18T16:20:31.049589500\",\"actStartTime\":\"2025-06-18T16:20:28.656012800\",\"thinkInput\":\"- SYSTEM INFORMATION:\\r\\nOS: Windows 11 10.0 (amd64)\\r\\n\\r\\n- Current Date:\\r\\n2025-06-18\\r\\n- 全局计划信息:\\r\\n\\r\\n- 执行参数: \\r\\n未提供执行参数。\\r\\n\\r\\n- 全局步骤计划:\\r\\n步骤 0: [completed] [SQL_GENERATOR_AGENT] 生成准确的PostgreSQL查询语句\\r\\n\\r\\n该步骤的执行结果: \\r\\nRound 2: {\\\"output\\\":\\\"{\\\\\\\"message\\\\\\\": \\\\\\\"无法生成有效SQL，用户查询过于模糊且未提供具体分析需求。\\\\\\\"}\\\"}\\r\\n\\r\\n\\r\\n- 当前要做的步骤要求(这个步骤是需要当前智能体完成的!) :\\r\\nSTEP 1 :[SQL_EXECUTION_INTERPRETER_AGENT] y\\r\\n\\r\\n- 当前步骤的上下文信息:\\r\\n\\r\\n重要说明：\\r\\n1. 使用工具调用时，不需要额外的任何解释说明！\\r\\n2. 不要在工具调用前提供推理或描述！\\r\\n\\r\\n3. 做且只做当前要做的步骤要求中的内容\\r\\n4. 如果当前要做的步骤要求已经做完，则调用terminate工具来完成当前步骤。\\r\\n5. 全局目标 是用来有个全局认识的，不要在当前步骤中去完成这个全局目标。\\r\\n\\r\\n\\n\\r\\n当前步骤的环境信息是:\\r\\nsql_query 的上下文信息：\\r\\n    SQL查询工具\\r\\nfiltered_search 的上下文信息：\\r\\n    过滤查询工具, 根据指定类型执行向量搜索，例如 documentType == 'table_schema'\\r\\nterminate 的上下文信息：\\r\\n    Termination Tool Status:\\r\\n- Current State: ⚡ Active\\r\\n- Last Termination: No termination recorded\\r\\n- Termination Message: N/A\\r\\n- Timestamp: N/A\\r\\n\\r\\n\\r\\n\\r\\n\\n**角色**: 你是一位专业的SQL执行与结果解读助手。\\r\\n\\r\\n**你的核心目标是**：接收一个PostgreSQL查询语句，使用`sql_query`工具执行它，然后分析执行结果（数据或错误），并以清晰、简洁的自然语言向用户反馈最终答案或状态，并使用`terminate`工具结束交互。\\r\\n\\r\\n**输入**:\\r\\n* 必需: 由 `SQL_GENERATOR_AGENT` 生成的SQL查询语句。\\r\\n* 必需: 用户的原始自然语言请求（用于在解释结果时提供上下文）。\\r\\n\\r\\n**你的主要工作流程**:\\r\\n1.  接收SQL和用户原始请求。\\r\\n2.  调用 `sql_query` 工具执行SQL。\\r\\n3.  接收并分析 `sql_query` 的执行结果。\\r\\n4.  准备自然语言反馈。\\r\\n5.  调用 `terminate` 工具结束并给出反馈。\\r\\n\\r\\n## **核心原则** \uD83D\uDCA1\\r\\n\\r\\n*(源自原提示词相关部分)*\\r\\n### 1\\\\. **SQL执行与反馈**\\r\\n    * **执行**：准确使用`sql_query`工具执行输入的SQL语句。\\r\\n    * **失败处理**:\\r\\n        * 分析 `sql_query` 工具返回的错误（语法、语义、权限等）。\\r\\n        * **如果错误信息暗示可能与表/字段名有关 (例如 \\\"column does not exist\\\", \\\"relation does not exist\\\")**:\\r\\n            * 你可以尝试使用 `filteredSearch` (以相关表名为`query`, `filterExpression=\\\"documentType == 'table_schema'\\\"`）来获取最新的表结构。\\r\\n            * 获取到新表结构后，**你的任务不是自行修改SQL**，而是将原SQL错误、和你新获取的表结构信息结合起来分析，在最终反馈中包含这些信息，并建议可能需要重新生成SQL（例如：“执行SQL时发生错误：[错误信息]。经查，表[X]的结构可能与预期不符，[简述差异]。建议检查并修正SQL。”）。\\r\\n        * 对于其他类型的错误，或即使获取了新表结构也无法明确原因的，简要告知查询失败，并可记录错误详情。\\r\\n    * **成功反馈**：简洁呈现**通过 `sql_query` 工具从实际数据库查询得到的结果**，并做必要分析以回答用户的原始请求。如果结果为空，也应明确说明是查询实际数据库后没有符合条件的数据。\\r\\n\\r\\n### 2\\\\. **交互总则**\\r\\n    * **主动澄清（针对结果的澄清）**：如果SQL执行成功，但结果的呈现方式或解读上可能存在歧义，可以主动澄清或询问用户是否需要进一步解释。\\r\\n    * **任务完成**：在基于`sql_query`的结果向用户提供了清晰的回答或状态后，使用`terminate`结束。\\r\\n\\r\\n---\\r\\n# **行动指南：下一步如何做？** \uD83D\uDE80\\r\\n\\r\\n**内部思考遵循上述“核心原则”，对外输出简洁。**\\r\\n主要行动是**调用`sql_query`工具，然后分析结果并调用`terminate`工具**。调用工具前，可输出**一句（不超过30字）核心判断或选择工具的理由**。\\r\\n\\r\\n## 1\\\\. **决策框架（内部使用）**\\r\\n\\r\\nA.  **评估当前状态**：\\r\\n    * 是否已接收到SQL语句和用户的原始请求？\\r\\n    * (如果SQL已执行) `sql_query`的返回结果是什么（数据、空、错误）？\\r\\n\\r\\nB.  **选择下一步行动**：\\r\\n    * **已接收SQL，尚未执行**：\\r\\n        * **思考**：准备执行收到的SQL。\\r\\n        * **行动**：`sql_query` (使用接收到的SQL作为参数)。 (理由：执行SQL获取数据)\\r\\n    * **`sql_query` 已执行并返回结果**：\\r\\n        * **思考**：结果如何（成功有/无数据、失败原因）？如何基于此结果回答用户原始请求？\\r\\n        * **行动**:\\r\\n            * **若成功（有数据或无数据）**：准备自然语言回答 -> `terminate` (将回答作为`reason`)。(理由：反馈查询结果)\\r\\n            * **若失败，且错误疑似与表结构相关**：\\r\\n                * **思考**：SQL错误提示[错误类型]，可能与表[X]结构有关，需核实。\\r\\n                * **行动**：`filteredSearch` (查询表X的`table_schema`)。(理由：核实表结构以诊断SQL错误)\\r\\n            * **若失败（其他原因，或已用`filteredSearch`核实完表结构）**：准备包含错误分析和建议的自然语言说明 -> `terminate` (将说明作为`reason`)。(理由：反馈执行失败情况)\\r\\n     * **`filteredSearch` 已执行（在SQL失败后）**：\\r\\n        * **思考**：已获取表[X]的最新结构，对比原SQL错误，分析原因。\\r\\n        * **行动**：准备包含错误分析、新表结构信息（如有帮助）及修正SQL建议的说明 -> `terminate` (将说明作为`reason`)。(理由：反馈详细错误诊断)\\r\\n\\r\\n\\r\\n## 2\\\\. **工具使用简述**\\r\\n\\r\\n* **`sql_query`**：执行SQL。`sql`参数为SQL语句。**此Agent的核心执行工具。**\\r\\n* **`filteredSearch`**：**辅助诊断工具**。仅在 `sql_query` 执行失败且错误提示与表结构相关时，用于获取特定表的 `table_schema` 以帮助分析错误原因。\\r\\n    * 其 `filterExpression` 中的 `documentType` **必须严格为 `'table_schema'`**。\\r\\n    * `query` 应为疑似有问题的表名。\\r\\n* **`terminate`**：结束任务。`reason`参数为给用户的最终答复或情况说明。\\r\\n* **此Agent不使用 `combinedSearch`。**\\r\\n\\r\\n## 3\\\\. **核心行为指令**\\r\\n\\r\\n* **简洁输出**：工具调用前的“思考摘要”极简（一句话，限30字内）。通过`terminate`的`reason`提供最终的完整答复。\\r\\n* **工具驱动执行与反馈**：使用`sql_query`执行，并基于结果通过`terminate`反馈。\\r\\n* **内部深思**：简洁输出 ≠ 肤浅思考。行动前务必内部充分应用本提示词的原则与框架。\\r\\n* **错误处理的专注点**：主要分析`sql_query`的错误，`filteredSearch`仅为辅助诊断。不负责主动修复和重新执行复杂的SQL。\",\"thinkOutput\":\"\",\"actionNeeded\":false}","timestamp":"2025-06-18T16:20:31.0601363","compressed":false,"entityType":"thinkAct","entityId":"28","updateType":"complete"}

data:{"type":"AGENT_INCREMENTAL","planId":"chat-1750234823783-5mywvgjh","payload":{"currentStep":1,"isStuck":false,"status":"in_progress","isCompleted":false},"timestamp":"2025-06-18T16:20:31.0621363","compressed":false,"entityType":"agent","entityId":"28","updateType":"incremental"}

data:{"type":"TOOL_START","planId":"chat-1750234823783-5mywvgjh","payload":{"parameters":"{\"message\": \"未接收到有效的SQL查询语句，无法执行。\"}","toolName":"terminate","timestamp":"2025-06-18T16:20:31.070203500"},"timestamp":"2025-06-18T16:20:31.0702035","compressed":false,"entityType":"tool","entityId":"28","updateType":"start"}

data:{"type":"TOOL_END","planId":"chat-1750234823783-5mywvgjh","payload":{"result":"{\"output\":\"{\\\"message\\\": \\\"未接收到有效的SQL查询语句，无法执行。\\\"}\"}","parameters":null,"toolName":"terminate","timestamp":"2025-06-18T16:20:31.073226500"},"timestamp":"2025-06-18T16:20:31.0732265","compressed":false,"entityType":"tool","entityId":"28","updateType":"end"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"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","timestamp":"2025-06-18T16:20:31.0777677","compressed":true,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"eJztXXtzE1eW/yq9TG1hXIh0t1pSy1vZKoZxdtlJSAYztbMVKCxLbfCubRFZzkBNzZYMfsjgF2BexmAMNhgSP3gEy5KNq/ajZNQt6a/sR9hz7rndfVtqPzFMkjWxHanv69xzz/mdx73d/ZcD8fOx9PHEgQb2IaBEQrIa1HQ1GNGDgVDHpT9/e+4/zx84fMC4aMS7023Jzq4DDV//5cCF9lgna4UfThkd8L+0Aa21qKwoWliTtSg02mbf6bZ0uwG1rEcr1vBCaXzOyi6X3900+2aLa+vw1Zx5Zs1Nmw+vldfvl6eHygMvSr1vy4vz5bmMme23Ho79tDpUGp+ysmNd37SbK2+twWdQzZqaLS8+hu67u4zUSeObbqMrDYNcgitd6VgqfaqtAwdVZTUUkMMBRT+lhBtUuUHVjsgRNRiNqDjtzgTV6+xub4cJJWGmRtqAObXG2rsM6Kq7oyOWumRX6EobF5BDB+Qj0tdO7TPS101/+PzsvzSeaDx59NSXJ88ehY+nzkhEtDnQX5peKE30fpXsSp9LGVCVaIcpmqOzQIXi01njnxqP/fHU8S9PnD1+4lTjya9ONsJfu2PiAFQDbpgzvaWx/lLhpvVw8sAZmEN3KmV0ppuA0uOdCePigQbl8IHWZKojlobem3xZI9msAWJi56B1oy0OTcjYzrjBpKIN+KJGeJUTMdaJz8TtTn5ndMVTbRewG1z95Zew4kBq+dkTkgFgibjW5cVl6+VlWOtKZqK8PlBeuoKysTxr9i2XR5bN0ds4WSYP5npf+WkPNLduLYFEmfmbxdzVYu6adXvAEZViLmM9mCbeF3Pjpe+fmmPZ8rPe8tAVaCi5ayFVLQYIT7q7C4XaEQaPYKRT3SAXHbGLTSQLIQ/LgUFby5+mBYOypojy51NPPxIKqaGoFkYVOt/W+V9H42k+Jl8L+fCBC7GUuFzH+Qqx+sc7L3SjSgSkpv9oOtX4hXT8xGdfnvziKEpVw+nU6c4vmxqkf2/rTCT/3CUpiqTIR2SpLtaRCGuHsBh/A9Ixmpz0O0AA1sqllCqYfXPmy0x5YdrM3iiuT1s9iw1ua5JUc/QyLFWDhNesyRfW6Fjx3X2x6G+Zy24b6tCan628mKFunQ55hbWb5uAwiIDZM4FSQDWf9oD41JXX7xVzL+iSdXexMpnBeqyBdS9fvrJWhM8LQyAh0PIfDkmsa+DOV5IsNfir8aXqkal3aO+IXdW8KwNIXXnxjXV35KfVCbwEOs7RjiSaSbd15y0IbDE3TGRWHj8wZ25jv4VCce0WKEpl4KrdSw/jPfSSYzOf9HSERBFTR+ZAxazsbWt0tLy+xJvhb/CIBOwCVTBHX8CHTXhYzM3DFbO/z1xYwZYatHx6GeBlkzbm8qtSYRT7XRiCKZnZe0RY2kh1tHWC6BC11oNZYr7ISL74oSN85Uv3F6xHAxKsHrLowaw1OQhLaovZTHmxHwYnvnFOCJ0B8eZIgQYhWRA7FeSM/lYtaGlk0Xx8hVYTxmcL2trWDrMwEme7jFgqfl6qXXe+xBL8A+CyCjMEKTTlwxLZPWtowFyYKL0sgKHjoj92vTIwao1Nlt48xum8uwZslhLJeHcHKNypSxcM6dNPpYPpWEu7cbYrft7oiB3EcQCMWto6t0lQqdALwEeDEEFY4izL5o1P8WqALNKpZLJdamLg2OBFBrxoNEg/TkxLAFFt3xpU/HmsKy320CCdSEppoceUEU+mEkaCqotjfWF0dYEJgRafHOWlgJIAqx0XnGue387fSPX1xbVHsGYA/MW14WIObMKEL9KTDYEK5sLb+nrpf6eejdn9QA8oBExUoCvghGivRGO1iZnyM0pZWNny9Byz0r1/y/RYj5atye+pJnxFl2A6CwJQmh/8aRV+hurrrclMqZCtciE2slv19ShAhVlAAHNspFSYJwkTxP03yCHrUc5cv2KOTIF+sqnfmHbLfyMpp08fQT4yMaDJFHMj5cXe4upSfT3WqsdiR1xW75p9WRx6AixyZboA38y+PuAvNTFvAA48R+4wJQOIAE1GZFlcKS8+MEf6zdFXUKdK/DjNOJKIcKX526VbbJ5cOrFCs60KTUwTmllxsdAHA5ZGlhhu8L4BEAAHrDuPrNe3CCk9K8tcD2swAziMBE7lS9MZjgJ33hJuc7r4wDYoiAPDqITjpcEVUHVbm7LbWXhYdfRSvESXVvPmzEtrfsafFCSk0rNu9g2b/XeKa/cYEdxVyi6V3742Z6aoJcgEtzMDw+bMsCizHhH5jaSSCJBsWb2j5v13xCh3/cvra5U7C+R20pCL/Wb2O7qM2A9UZ9+a9x9Wpt8xl+05GSgzP0orjz5a4Zk1uI6lY1n4W5q/X8kMgr9WLLwFiXMYIEyURBeZ++YRDcjGBtMImkHkErYik4cGGNb3lApvzPxT/CAMgBXuvK3ceYMfbg4X1yaFYUCdiiuD1t1H5lIvLepSP5eR6TkwqFQC7HW03Jy/Yy38gLKeWcXZ978qvcABSDsqAwPQ3JGAWhFfeIgOSWbVHFqp9A2TALOWHm9hfRz4iZIJdu3Wm0/MhbvW7RVoRRFT6dU7YAL1wHCgD40ic5IdmYEIq/T6KtWpWvMgrTnK4sNes5AXlpr5y4KGg3Q2//u/Np5sbAa5th72mEvXkRFX+sz+Hyr3ZsB7Ifgqwcjzj5ubGj9vPHZKqofaGK2AbQYZRGKGH7voxNgwermc4QOxj1XzdvQEfZP7ueLqKsdLQGtibG6kkimU310XO2VOkYAJoOAwAWIMrUTlyhrAEoEW05BxWmySfCvXZ91bIifNGhwmLAZSqvinEf/APFjZ65UXQ+XFHpuFThPSQOtJBkRVgDAoAYlDTLoLSvrUmn8CC/nT6pRdzi8XpkGbOWVsDlw/mTwL1UHOUBBJIseGS8+WqAPgjiOvxC9bWNyRWI/EQ2pEIZPID16dWeRAgE3fQWkurv2vAaitR9eZdRkftqsrDFRoMpODIjQ5HILOiWsSn7j0Y/8NqXT/jTUy67IRsKKQBdFyKkKPVLHwDMweGR57VNUdtTQ1W+m5CaJj9r0GLhXX+hFcl647muk/fDXO1yGHx4YPe7y0T70u2qFt0RZ0aSOjIVoG8N8Fy7Atwmhq8FMZXygv1lDYFcMAFmK42MFPDnZ90342Husytkmq5pJKZhRYV1wZrTzuN6emyleGrHuLGxBZZZ7rSJjATFQe360dG/xhLm3vekH1EGGYpQbkrkyvALA4SlcldRQiMZG7/+in3Khdb0MXgRvJvrnKA3AHH5jProkmp/mbbiN1qZn6rnX9BITxdQMqN7Jg56wMOGBAYp/oDAg+BG/aePFCCpxdcHqbKexqFtetmVC3mMs3e2Ws+bDULK4pfAeZaXZXFnobLGf6iisws4wwLKBg8+YRRjPhHWeCmR+H0QEjyeZiyoZpgMgEJ5ig+aO/enVKRD7yN9A4LvUT4oHLCnYX55UyYl0weUoDoIXLrIrwirj67qY5kLcerZqro2yNbz4QnFor8wDMBTjipTlQlWxpIWO96eGyAl+cMJx80NLVt1amx3VPGVxh+mz8JTSu3BsLyoCeEPpCDwIZ5YWl4toohgOse6IITdn8YDE/Ur48Y/3QR4RAI7C9GNxdZT7P1WlYQuivcmWOrAwwFEUKNH10rDSTB1XFSTOPHFF6GmITlhNgPgD5TlVeIYYezOsDApxMmOgqC4TbqaMvu9OUC8LcXBxjrBOGkXDTWXTNm7DjSaXOc1IaQr8GqSrsdHo6aXR1t2Pffzl9IMnGOX2g4TR8O336QMpIp9qMb43E7zuTf243EucMvNrAypjUNTGh62JXv3Yvo2iya/jn1NHfft6IHw5Tl+3Gt7HOODRNpqiSfESJKKoeVsKhiKKp4WBUZVUTbRAyQk1eSVf1iBLUg1QmKIEzkqgIzoDxZHt3R6dIYgJUztPuW2RILOU06Yx1uIXdncAwUMu29KWz8WTCcGolXHY7lc3hXDE/U/ruHth58+VDc3LJzF0uFp6UHvVg8V8P74KAFgCEsxdSyUR3PL0NCij4yT8zb/bQwOiU3V/DfNDwa+vObJ2C/w4dpmsIb+vfQTRdydyAElWR7RK4DNfMob7ykxUoCSqq02b8B/NWP7joUAVKQm4bvDbQTy2hJAwlu542JnJBANriXWeh4eaTLhWmygvTFBYghDGwuQT/Ah0dgURCOn++oaOjoatLAoPcQAnQIObNZb1BluFnMyLbOtOaP4XJb41UCkhLnO3s7lA2JbByeQFcPYKvAMBxJfMO0ErVzKVRdPEh8s9dpToAo3tAjboLahT1v22CtkXKpqu3fXkdXTTHF8zh26LI7gEHgrvgQPi/FXUHDEjbSS1/cs51pM92JBNtrW1GYlNiiusL1vgKSe/uZg4+iJECmD4LY7UnujaXRfN+HjwaMNR7IfbapkNtwGZH6tGyvreodcTa2s8iWhhnW9tj57ZNEOIiOEbPnpTXnuKuxuB31uho3alj//bZIfR1ll/RFSiCUMscvQ5Fn9lFdAWKzKUeaA5FX322KdRtk52hXbBTDuxEaLdJSHg3cBbYM/hoA9A32I7dpnTwqCb7qHJvBnF//omZy9WhOVp+RVfrlN1bIG75k6lzOzD8ewZgirybJdACmr730qDsxrppeiDyASRT2Y1tswkhS7tXUpo2Yp1bYw64WNbCVXCNIAbDGAQDykH6uoV0bpcju7F1EXPm+Q78jk350Bq7tB1GAGJWXgw5jIDPZh+syG3zZQYYgXERlE5+j1/V99ZacEC+bYPgYcc+e2kSnJC1rdV4G9Y/DtHxVhBmZu+bhfzWxn9zX9noTBips53JzYdaeGxll83R5T3jrV24M95SanN3gn8BfarO9PkteNpvDuWtySwMhmlbJxzo6JBYSm4CYwA5+L66h55H1y507/01TjBN21+Drfm+6Zg4WJrX3iISKw9dMe9jJFbMZUr5Z6Ddh4v5IfyEQSQLiuuC8Gkob46s12lQmhs0x7JQuy506HA5+6L0PF8Xfm8MYDH7TmWUyHsfGfV643vhjG8r1Np6ot5Q632m6LE/kV2ogBb4AG6KvgtC9MCOYr6NKaEIZIeQsAc+SCrW2XUhmUpvrZvl9bHyu5u487iKyVBzCiDye7K8XOVUFt70zZXmrlXu9UN90NJdU9YV6zzfvbVXYN16AlZp16N4smNbq4CQHSP5/xlkx85sSK4zlwupNjzH+nvj0vHO1qRT/tMqYGahMr7Azwm866NNC0ygO2zgmdLjCadZKKZpCSMaDBiROARtWjwWiMZDagBojSqKFtd13Q0+WFL1hAdwknFMycUE4t29DFbra5499kn1Ag9VXVMiQU3HjG9U80v1RvRoRAnprKg1mTqFJPgOv3Eu2EuRQOTJ5J9ZJUaiX3KXNVdCsiK7TPDNwVJFlAp3iNqkJavl5Bw1T87xcA2EKcSB2gJVKNiYEC2o6JosR6OKf/fM5WlQWEF1fqohEFaVYCgSjoT1qCzLPF1fm1nyI4+BYkMwxEp8MjK0E3Ds34750xXaqN8w9UtJf29WgHXpstEnWqcasI6yHNmAIbR2fiMrG85VEdfCEwOyAYMbjBQUWnkjJv+Z1EQwtmDKgvBUhRm+a+iJDlgneBp+w+E88AqwVb5xzxydweMRrwrgsbmYJLjiDeRQV83aMYYNwXD1GnlHub6K+yS5eXP9Sh0AJOLq+n1zIH/I5aXgffrzy+PtOd3eGLJ+eI44nrvsNPBx0Ko0y9ONdW3VnJmAn0omg0df3jwrDT4HA+K/zhHiR22BLuhelbPQECXFqTXmNFU35eE1qrWL6WsOuadZbdFcfpDFUf66seswPSfZuItnTr3n4FzO+hgbJS63ho2EGgi3xFsCWiisBnRFbQmowZCiJRJK0AhHsR63/v5GQ47oshZVdFWHP1oo4ms0oqoSDSn7RmPHRoMhJBgN1b97Ebg+gNGQP4zNkH9BJmOjkX5GJkMRu/gYJkP+BVsMwtrGL5rwWDTEd+Nv8WRs3mWEj8XwkRpdKKi2GMqv1mCEo5FoSAvJgZguywFNCccD0aAcAhg0wqphKBHDaMF6mxuMsAJGQ41qWliDgC7obzCCOvz+Cg1G8OcQZXwwg6FsYTA++8yfrC0MhvILMhi7jDHU6pn8mmIM5RdsMHYTYuwbDN5Uj7QYWjAWCehGPBbQWkJKIBbTY4GWcCwS1SJGqyIzDd/cYGhhcIAjER1+5EjY9wQi2KUgqPmv0GD8LNJS6ocyGFp4C4vx1e4sRvCXFGPsp6VqTYam/z+zGUp4I6Oh+hqNUPRXazUMcP3jrbF4IBTXEgENAotAS6g1GlBjuC0RVJSYto28VFBXlIgcApTTQhFV97caYUXXf41hRuiD56XU4PuHGQg4+3mpXdmMDRKCH9tmuCu4n5fam7xUcf1KZTxD2Sn/Nd6PMTzWojUR02VVUwLhVlUPaIloPBBtBRhsibbqrbquGC0yQw17P/yb9mMwh8aLDKXFm4/oUUQXYYWdvkFaXM580954EZ/aFfPeIsBuZmz64xflngm8535gDW+3Y2etvKtmZu/gxDIFEBc8y7Hylp5zwe76NufHzNFZKPn8+O8b6d750vdP6eZ6kdsgtObYdwer5ORgMTfuwlYxlz8oH8QbCheWzLVbHvZV3bcVVkORKN/oh/n9Ae9MdGbG7zGHqdV5Z3JIOtok4aWzWjqpn3ek7HTnZye//MJdR3aJTU9yqfsUaJOOnvidJE5LwmlLB/+xal7/ePCfalbea335XZmumoLS/QEfWOY5x8LuwK7qG5aWn3cys0v8xBPwzDn2UxkYdXpNx85xQcG/UMUaX6fDIk6VjTSXxqAnozgXnSNWzhUSGOer+EwB/E6iizP1TL+qW7CxrW3x7nZBgF3F9r+7LxjRVFkO6YCrmhpRQ9UK+lkq2XGMM7jBJbSal3SDkM1ROjJrTs4JrC2uXINrVQz2ajaIkecsVi2T+RzSzFETVkR01c5shBNKIt4CTqUWCCcMLaC1tLQEWiJGPKDGWxNyq6a0xFpaXWqgt6Zkdyq+wdmawx8BMZRtIEYtQqD2i0egdo8NAAx883vb0KC40KBqewEMn3rnsodwIPbrSO4Gt/3tEAvErjcHAns8Hxz4cCoP+i5ruhwMhyKhoBLZpsp7+OWn7+W34E8N2DP6e2q6pgVDra2ReCChtmD8GNQDuhw2AnHFCEZiLWoi0iIc/f05aHrw5+gcRDRFDys7BICgiwCRxC/cMyCJxttxPpZjgGP5YMHH9QnUkK4Eo7KiqZomR6LhPfQJNuToR8WHWNxobY0beiAeUtUA+D3xQIscCwbkeCQqJ3Ql1KoJN1nuGh/WRj4qPnA84PEy6jmFy7vX/6gcDe/UAdgT/edz+NSewR7qepDuPdpDBfdVZBxmK63etQK7xmsDBZajclCLKtFIUI+Eo9t16j2s2VhfMwX+WNmPp6+KIkeNmB5oUcMQ4asxiJ/AgwlEg3prtNXQo7GEcEfubvW18t3d99BXR3pAQTkHby05z7KsUVN6dhExHJ+WN5m3Hva6j9nDWxTuFNeG8SGOwh0RjtrirXOTGXNshCj6l5Nf/vEr6bf/YQ0NivdI4nN4WDsQqVKhd3NdD4aC+qa6znRWkvzyhoc3KkQL7hQiWAhpKQYU6WQa6rPvnnpV6T0HVIRrG9TfAISkHaEQ79kXjMRCO2H46UHvdTfzKv0DNPSUVaWtoYJ0EPc5vaN6Y6CV12YuV1yfBonj1ewl32A9/BZib1CUiRgPCQSyaux5T9UNlXAR5NEFlNrinYAuv13VC5UiPe5FJvtVvlQ1Zu8ait07zjaC4rAWViJ6RFWD4FhEt4RiR4WJbvZETwGcq1gOYOxwEHEDvtrYg8/wGL3qY+f+Dg6XkYhHwzFZC8SMWDygJeBPTNfA4TIikagaU1vkqHCj/BYAfoayyPz9AceS3Z1poucvXIqRn+wyu8qP77uPxHKLeGzj7ODVNEJwEqor9sjxuNFFY7KHfOGnDnqwsruO7Flt9LBg8UnR7IGX78zsEj4X3nowTU8XdB9i51gg9igwBsTHAH6Mi2nXWtOz/ughycLj88jBkQ5eOihRr57n+Isj/BV+xAfyN/3x2LHGpiZ8plky2c7fP1D7VDIs/CqWguK0keqi55Kxh9kBYdLpA5eoV/ZYNP+3IQgP6o9EtXAoZFdv3ORp/aEj0RDYfV2l56Jt3jNW1oPgLlDlzfoNAxHBoKZgVTAeyRR/Nja9kQL8hP03Aey/CWD/TQD7bwLYfxPA/psA9t8EsP8mgP03Aey/CWD/TQD7bwLYfxPA/psA9t8EsP8mgP03Aey/CWCP3wTgLPO23gEg5hwllnRkvjeJBVqwW1kG+kNc5SmYWR8AfpKrArbYSRUxTb9JgRugJfjqO0kWioT7pQltUlmicO/J3G7iMXxE0yNBTQ1vJ/GoHwlpuJe6nbwj1g0rqqpsnXfEt48qihrU/POOZ+zMo6rXvg92k3fX+r8bFk0cC1NB8OlNthRU7MHrWJWtXseqHwmHZFlhr1nd5HWsQeWIHAlFwnJkp69j1X+dSVi6IMkNe/UiZAdiF2edLJy9M4EiQfSeTHZ3JiS1Qfq7Is3HyEIrPAu96Zug9zPS/68z0ujlMfds8+yto2lu3vdnl8v+1SWiMWlyozz4qr6+QfJNSNdaPXyXxGLBvPrcGrwmiANLaLPbjTDz5SSlyccemcWnA0HPuRf+uIqLwLS72ZGWZi7xtGALVxASe9+Cf05wJ6Iuxl52JFoZv1deXKS8DsTfxcIs5UUwrcbc4ao3ujthnDk6XHma5Xns+XEIZ6E3+yUarCsi0I0fiEAk4cFUMT9TzN/w8AP97r5Z4CyFHhgcTWYawM68lJp9LFAzt0Cc6QJznOjF7YK/mh498WfX/F5Qj5kQMBno3TMUJEYBXhLMCe75oM8iFnMFRIZlDHusH3ogWOHTABSWaDUxVrrBjRQngw3NewOglQjBJGFJJXFN3dRU0OmVZWBxeT2tqowsbwWwKuGjZmc8W/e0jC4kOlS46yaJC8dyNPcgQPI03M4eRH2dlR+DoaHQjY5YXgPDp2z/ofqqnQpRl2g0ykL8zyMJfljamBVTYpE5IpsqBQkYyYtHUtwOzZmX5Tezdva7wQYoiTIZtZyuSmTaypSltN/fMBjFjDPmoh9cgQCUdoCciN3uGQZmBo/ac3MwcQd4ZI4uoj+RG4HA/BMn9Yd5zr7XUh0HYvt9ZoDIRpfUmUxLxsW2LnCiDkvs+AyhXXXhoaoJEikg0DAmHuFZelBevEUMrUlIwdCFWSffBhRhsE88OVyb/PjUe+7Lx1YcaAa2gOnle2zgmt1eomyEvbdyuZZUZzMEKjs1KRXrKGYBkHcaDDdAK4geyhJ7rxJIANtUK2eGMPW11A9CCdeI/7BauHEF8A79sjFEUnhis3DTHMuWf2AeBpMLBL3JOcJDklZ0Ddh9EegX5O/bZg+x0SzkywsLtLjcFxsYxjm7+dIsLS6I9o+ZSQcBAJHMUUDgKVvUJr4WheYMMAr8IpaGHgKSv/7TGSeJ7ohS5XGvNTmFCY7vn0K1rzGRsr5kLi+Yq5exAyKOstmI48Cx+SeEPj9mHvjJr7m4guDJXtxBfoOgDOAZ3TaHX4Mk8U3HfL+4YMWVKb5xxzZ7EDruPyKHizI85vWrpalZ7qgw/eQ8hPmwI6k0UHnxqXWlr0ajKV9lw4eT5DGvZ0sjS/X1tM3ro9PFwoi58LByr89JrXMK3t0BibOZOsmkCInpmaBsDd9/Bv0BRJgfrzY9NuaTvlMfoDul53k06StTZn6c+EBuOAgnH9ZLC0i59WoaN1xZGp+2CpxDV/67fmRy8SA/QrOXS2DAzKtzTmaWkpxEHfbKrw8S0jLSHXAmBjOHpN9pQeylp8rD8pMPhC/ZYTJozt9FVZl/BtCI7GN4I5KATRYfV+4s8KBOSKSX18EYY+RKdrpmvUnjyR8ncsFfn8qDeApr7Cyfm5QmMw+iybwfnAJbP8en4Zus1T4NGUUxtna3TihPiN708B322otrRDkwEKItkBiWVwcbOZFxHQrKJlK6sdLzg/nuBbAN1BNBQLCtTBGHYJEggKOEJck1UEJrSy4JJwH9SrLrNaaxyk3kbFl5y6tXe2/19TCCuMMM0QwtIVGBopRhDmoWwyV2+pNlXRHfif7sjHV7Hj3PzKB17Tn3LSZ6KU/L+hc8Cu4K8J0nnj/NEpNoMZhY2i2OHkExqOcpYCEp7BwtsCWF73gsv+KOVHbJ8QocP61KbfkGmd1DnaMI7LVwqAuHJK+QcceAfEmIFQqZ4krW8b7RxX6ex/PKrg/ubMH9lmZCTHJEh9azZjK4YfLK8TNZKn6Cvb3OcY/c2gI0MiGzvSfwC7lCM26Qm+RRL7elQIaInHV8M9rmKPVBWwGUhDoEHUp1dkp+wjFr4tmaQ96ZeaGZMxoFVODtdqbIJZvpHi4BnZKdHPwErY+9Htz/YzaI1oMrK0MQa36G9yJg+4YSUssuH3cLCq/N2hCaZfksHpg5ZDm4W+u7MzKkwD97vPU6dGdYiXcTBnnvsp6Hb3S0gk3q0AbUOTa3mBsnSS3dvl5cXSWnlIw4uYLC+R3vv6qlcDwtigO4B0OOwxmOJrbTix4MG4J8XtweAe8QkGThoY9T6JAtCGiN68pjxem5P+GOnOiIVvGIhnE9lQK0uwrg5dC/Fcuy/F1mXJ6YJ7T8CnG1iigaCayWsGMtrjr5kTQoATXubzE/rSo+J7/BTyaoZBsyQRrGpkBnP9xp+m2LulqJE56cQ0AkNUKTOVgjFNXQs/yKtN9xV7n7z9mARs5aHBcddJbwY0xgjPUJp4T134iBGJOJbqh9ngtRHUQtt2BefQ7km6NXHQfYZfh7Mxm81VKh3/ZcUaic/QBu+bjXJu6Ek6PuWrsqeKTckZArwDK+9wrkiFEvrMD8zFHctHFSUHzZ7WNj7jEh321wTNf0AYeIdsE3oONJHodaFCgHQ0j1azGEUtSUjeHBF9tix2oTvZJXXSX0s9lK8Zw+ra8oFTYKgyb67MpLlHuWvPvyyHa+NT9rPQKgy0vVO/SOn2L332ybKLalTggJckSnIbazsc7zYsx9xbXzbJ1jl4V7jlfiZN3MmWE0FUxLSfTEQ3V8icERs8P4quMSzjpzmeNnxfhpPEy85MF9LRZmvDIn7pNzHfZsDQCF6K4yHbeu3wU/FNxV6yEmFJlfiA4inRSs2ZOneEx0sAGiOSv4jgPN3T4tR0yoOVZaef4SHd/q9BG64dUpIhu7WBjH7DzZwxpSPBkvNhT56ssvYapCbEmMkX4cfCRVHR54z5MD4slQknSWqGIJyJvW67nS5RWaIqUjmUp4HVIHP6uVGtQWFlvUadRl8ODfTJXfPKHYDHAQWI13mbJcha3Ww9aDy463+HM6kgAOsO2J0kZhbaoYbTIlH5wT1R/m3MHuaNnu4QLc79ZlZTs3NeGetxYN6dHQNg4XYOVwJByMhLY8XMD20oOqGg5tdLgA//s/fJUMTQ==","timestamp":"2025-06-18T16:20:31.0887683","compressed":true,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"PLAN_FULL","planId":"chat-1750234823783-5mywvgjh","payload":"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","timestamp":"2025-06-18T16:20:31.1010774","compressed":true,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"full"}

data:{"type":"THINK_CHUNK","planId":"chat-1750234823783-5mywvgjh","payload":"计划执行完成，正在生成总结...","timestamp":"2025-06-18T16:20:31.2755471","compressed":false,"entityType":"thinkAct","entityId":"default-agent","updateType":"chunk"}

data:{"type":"PLAN_COMPLETED","planId":"chat-1750234823783-5mywvgjh","payload":"根据执行结果，用户请求为单个字符“y”，系统无法从中提取明确的查询意图或分析需求。步骤0返回提示：“无法生成有效SQL，用户查询过于模糊且未提供具体分析需求。” 步骤1也指出：“未接收到有效的SQL查询语句，无法执行。”\n\n执行过程中未获得任何关键数据或有效内容，问题在于用户输入不清晰、不完整，无法支撑SQL生成与执行流程。\n\n总结：由于用户请求“y”含义不明且缺乏具体需求描述，系统无法生成并执行有效的SQL查询，建议用户提供完整的自然语言请求以便进一步处理。","timestamp":"2025-06-18T16:20:36.7226947","compressed":false,"entityType":"plan","entityId":"chat-1750234823783-5mywvgjh","updateType":"completed"}

