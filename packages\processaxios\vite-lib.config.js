import { defineConfig } from 'vite';
// import viteSvgIcons from 'vite-plugin-svg-icons'
import { resolve } from 'path';
import commonjs from '@rollup/plugin-commonjs';
// import visualizer from 'rollup-plugin-visualizer'
// https://vitejs.dev/config/

export default defineConfig({
  plugins: [
    //解决引入commonjs模块后打包出现的{'default' is not exported by XXX}错误!!
    commonjs({
      requireReturnsDefault: true,
    }),
  ],

  resolve: {
    alias: {
      // '@build': resolve(__dirname, './../../build'),
      // '/#': resolve(__dirname, './types'),
      // "/@": resolve(__dirname, 'src'), // 路径别名
    },
    extensions: ['.js', '.vue', '.json', '.ts'], // 使用路径别名时想要省略的后缀名，可以自己 增减
  },

  optimizeDeps: {
    // include: ['@/../lib/vuedraggable/dist/vuedraggable.umd.js', 'quill'],
  },

  build: {
    //minify: false,
    lib: {
      entry: resolve('./axios/camunda.ts'),
      name: 'processaxios',
      fileName: (format) => `processaxios.${format}.js`,
    },
    rollupOptions: {
      // 确保外部化处理那些你不想打包进库的依赖
      external: ['@vue/shared', 'element-plus', /^element-plus\/.*/],
      output: {
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        globals: {
          'element-plus': 'ElementPlus',
          '@vue/shared': 'VueShared',
        },
      },
    },
  },
});
