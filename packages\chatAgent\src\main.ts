import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import App from './App.vue';
// import 'element-plus/theme-chalk/src/var.scss';
// import 'element-plus/theme-chalk/index.css';
import 'element-plus/dist/index.css';

const app = createApp(App);
app.use(ElementPlus);
// 获取 Element Plus 的 CSS 内容
const elementPlusStyles = Array.from(document.querySelectorAll('style'))
  .filter(style => style.innerText.includes('.el-'))
  .map(style => style.innerText)
  .join('');

// 无界环境下手动注入样式
if (window.__POWERED_BY_WUJIE__||window.$wujie) {
  const shadowRoot = window.$wujie?.shadowRoot;
  if (shadowRoot) {
    const styleTag = document.createElement('style');
    styleTag.textContent = elementPlusStyles;
    shadowRoot.appendChild(styleTag);
  }
}
app.mount('#app');