// import Message from '/@/components/sys/Message';
import { ElMessage } from 'element-plus';

interface CreateMessage {
  info(content: string): void;
  success(content: string): void;
  error(content: string): void;
  warning(content: string): void;
}

class MessageProxy implements CreateMessage {
  protected message: typeof ElMessage;

  constructor() {
    this.message = ElMessage;
  }
  info(message: string, duration = 3000) {
    this.message({
      showClose: true,
      message,
      duration,
      grouping: true,
    });
  }
  success(message: string, duration = 3000) {
    this.message({
      showClose: true,
      type: 'success',
      message,
      duration,
      grouping: true,
    });
  }
  error(message: string, duration = 3000) {
    this.message({
      showClose: true,
      type: 'error',
      message,
      duration,
      grouping: true,
    });
  }
  warning(message: string, duration = 3000) {
    this.message({
      showClose: true,
      type: 'warning',
      message,
      duration,
      grouping: true,
    });
  }
}

const createMessage = new MessageProxy();

export function useMessage() {
  return {
    createMessage,
  };
}
