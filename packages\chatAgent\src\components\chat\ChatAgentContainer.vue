<!--
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
-->
<template>
  <div class="agent-container">
    <div class="agent-header">
      <Icon icon="carbon:bot" />
      <div class="agent-name">{{ agentData.name }}</div>
      <div class="agent-status" :class="getStatusClass(agentData.status)">
        {{ getStatusText(agentData.status) }}
      </div>
    </div>
    
    <div class="agent-content">
      <div class="agent-info" v-if="agentData.description">
        {{ agentData.description }}
      </div>
      
      <!-- 思考容器 -->
      <div v-if="agentData.thoughts && agentData.thoughts.length > 0" class="thoughts-container">
        <ChatThinkContainer 
          v-for="thought in agentData.thoughts"
          :key="thought.id"
          :think-data="thought"
        />
      </div>
      
      <!-- 工具调用容器 -->
      <div v-if="agentData.toolCalls && agentData.toolCalls.length > 0" class="tool-calls-container">
        <ChatToolContainer 
          v-for="toolCall in agentData.toolCalls"
          :key="toolCall.id"
          :tool-data="toolCall"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import ChatThinkContainer from './ChatThinkContainer.vue'
import ChatToolContainer from './ChatToolContainer.vue'

interface ThoughtData {
  id: string
  content: string
  status: string
}

interface ToolCallData {
  id: string
  name: string
  status: string
  params?: any
  result?: any
}

interface AgentData {
  id: string
  name: string
  description?: string
  status: 'not-started' | 'in-progress' | 'completed'
  thoughts?: ThoughtData[]
  toolCalls?: ToolCallData[]
}

defineProps<{
  agentData: AgentData
}>()

const getStatusClass = (status: string) => {
  return `status-${status}`
}

const getStatusText = (status: string) => {
  const statusMap = {
    'not-started': '未开始',
    'in-progress': '进行中',
    'completed': '已完成'
  }
  return statusMap[status] || status
}
</script>

<style lang="less" scoped>
.agent-container {
  margin: 16px 0;
  border: 2px solid #8b5cf6;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(139, 92, 246, 0.02));
  overflow: hidden;
  animation: agentSlideIn 0.4s ease;
}

@keyframes agentSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.agent-header {
  background: #8b5cf6;
  color: white;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
}

.agent-name {
  flex: 1;
  font-size: 1rem;
}

.agent-status {
  font-size: 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 3px 6px;
  border-radius: 10px;
  
  &.status-not-started {
    background: rgba(107, 114, 128, 0.2);
  }
  
  &.status-in-progress {
    background: rgba(251, 191, 36, 0.2);
  }
  
  &.status-completed {
    background: rgba(16, 185, 129, 0.2);
  }
}

.agent-content {
  padding: 16px;
}

.agent-info {
  background: rgba(139, 92, 246, 0.1);
  border-radius: 6px;
  padding: 10px 12px;
  margin-bottom: 12px;
  font-size: 0.8rem;
  border-left: 3px solid #8b5cf6;
}

.thoughts-container {
  margin: 12px 0;
}

.tool-calls-container {
  margin: 12px 0;
}
</style>
