import { getStorage, setStorage } from '../storage';
import { TOKEN_KEY, USER_INFO_KEY, PERMISSON_CODE_KEY } from '../cacheEnum';
import { UserInfo } from '../store';

export function getAuthToken(): string {
  return getStorage(TOKEN_KEY);
}

export function setAuthToken(value: string) {
  setStorage(TOKEN_KEY, value);
}
export function getPermissonCode(): string {
  return getStorage(PERMISSON_CODE_KEY);
}
export function setPermissonCode(value: string[] | number[]) {
  return setStorage(PERMISSON_CODE_KEY, value);
}
export function getAuthStorage(): UserInfo {
  return getStorage(USER_INFO_KEY);
}

export function setAuthStorage(value: UserInfo) {
  return setStorage(USER_INFO_KEY, value);
}
const getItem = (key) => {
  const data: any = window.localStorage.getItem(key);
  try {
    return JSON.parse(data);
  } catch (err) {
    return data;
  }
};
export function hasPerm(permission: string) {
  const points = getItem('designerPermission')
    ? getItem('designerPermission').split(',')
    : [];
  return points.indexOf(permission) > -1;
}
