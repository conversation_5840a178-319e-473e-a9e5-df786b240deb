import {
  PROJECT_SETTINGS,
  TOKEN_KEY,
  USER_INFO_KEY,
  HEADER_TENANT_PROJECT_KEY,
  DICT_KEY,
} from './../enums/cacheEnum';
import { ResDict, UserInfo } from './../types/store';

interface BasicStore {
  [TOKEN_KEY]: string | number | null | undefined;

  [USER_INFO_KEY]: UserInfo;
  [PROJECT_SETTINGS];
  [HEADER_TENANT_PROJECT_KEY]: string;
  [DICT_KEY]: ResDict;
}

// 限定BasicKeys的类型为BasicStore的key TOKEN_KEY | USER_INFO_KEY
export type BasicKeys = keyof BasicStore;
