<!--
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
-->
<template>
  <div class="think-container">
    <div class="think-header">
      <Icon icon="carbon:idea" />
      <div class="think-title">{{ thinkData.title || '思考过程' }}</div>
      <div class="think-status" :class="getStatusClass(thinkData.status)">
        {{ getStatusText(thinkData.status) }}
      </div>
    </div>

    <div class="think-content" :class="{ streaming: thinkData.streaming }">
      <pre>{{ thinkData.content }}</pre>
      <span v-if="thinkData.streaming" class="typewriter-cursor"></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

interface ThinkData {
  agentId: string
  content: string
  timestamp: Date
  streaming?: boolean
  title?: string
  status?: 'not-started' | 'in-progress' | 'completed'
}

defineProps<{
  thinkData: ThinkData
}>()

const getStatusClass = (status: string) => {
  return `status-${status}`
}

const getStatusText = (status: string) => {
  const statusMap = {
    'not-started': '未开始',
    'in-progress': '思考中',
    'completed': '完成'
  }
  return statusMap[status] || (status ? status : '完成')
}
</script>

<style lang="less" scoped>
.think-container {
  margin: 12px 0;
  border: 1px solid #10b981;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02));
  overflow: hidden;
  animation: thinkSlideIn 0.3s ease;
}

@keyframes thinkSlideIn {
  from {
    opacity: 0;
    transform: scale(0.98);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.think-header {
  background: #10b981;
  color: white;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  font-size: 0.875rem;
}

.think-title {
  flex: 1;
}

.think-status {
  font-size: 0.7rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 8px;

  &.status-not-started {
    background: rgba(107, 114, 128, 0.2);
  }

  &.status-in-progress {
    background: rgba(251, 191, 36, 0.2);
  }

  &.status-completed {
    background: rgba(16, 185, 129, 0.2);
  }
}

.think-content {
  padding: 12px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  white-space: pre-wrap;
  background: rgba(16, 185, 129, 0.03);
  position: relative;

  pre {
    margin: 0;
    font-family: inherit;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  &.streaming {
    position: relative;
  }
}

.typewriter-cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: #10b981;
  animation: blink 1s infinite;
  margin-left: 2px;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
</style>
