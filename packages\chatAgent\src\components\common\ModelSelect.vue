<!--
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
-->
<template>
  <div class="model-select-container" ref="containerRef">
    <div 
      class="model-select-trigger"
      :class="{ 'is-open': isOpen, 'is-disabled': disabled }"
      @click="toggleDropdown"
    >
      <div class="selected-model" v-if="selectedModel">
        <span class="config-name" v-if="selectedModel.configName">{{ selectedModel.configName }}</span>
        <span class="separator" v-if="selectedModel.configName && selectedModel.model"> | </span>
        <span class="model-name" v-if="selectedModel.model">{{ selectedModel.model }}</span>
      </div>
      <div class="placeholder" v-else>请选择大模型</div>
      <Icon icon="carbon:chevron-down" class="dropdown-icon" />
    </div>
    
    <Transition name="dropdown">
      <div class="model-select-dropdown" v-if="isOpen">
        <div class="dropdown-content">
          <div 
            class="model-option"
            v-for="model in modelList"
            :key="model.id"
            :class="{ 'is-selected': model.id === modelValue }"
            @click="selectModel(model)"
          >
            <span class="config-name" v-if="model.configName">{{ model.configName }}</span>
            <span class="separator" v-if="model.configName && model.model"> | </span>
            <span class="model-name" v-if="model.model">{{ model.model }}</span>
            <span class="fallback-name" v-if="!model.configName && !model.model">{{ model.name || `模型 ${model.id}` }}</span>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Icon } from '@iconify/vue'

interface Model {
  id: string
  name: string
  model: string
  configName?: string
  provider?: string
  description?: string
  status?: string
  type?: string
  maxTokens?: number
  supportStreaming?: boolean
}

const props = defineProps<{
  modelValue: string
  modelList: Model[]
  disabled?: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const containerRef = ref<HTMLElement>()
const isOpen = ref(false)

// 获取当前选中的模型
const selectedModel = computed(() => {
  return props.modelList.find(model => model.id === props.modelValue)
})

// 切换下拉框
const toggleDropdown = () => {
  if (props.disabled) return
  isOpen.value = !isOpen.value
}

// 选择模型
const selectModel = (model: Model) => {
  emit('update:modelValue', model.id)
  isOpen.value = false
}

// 点击外部关闭下拉框
const handleClickOutside = (event: MouseEvent) => {
  if (containerRef.value && !containerRef.value.contains(event.target as Node)) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.model-select-container {
  position: relative;
  width: fit-content;
  // min-width: 210px;
}

.model-select-trigger {
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 0.375rem;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 36px;

  &:hover {
    // background: rgba(0, 0, 0, 0.05);
  }

  &.is-open {
    // background: rgba(0, 0, 0, 0.05);
  }

  &.is-disabled {
    background: transparent;
    color: #9ca3af;
    cursor: not-allowed;

    &:hover {
      background: transparent;
    }
  }
}

.selected-model {
  display: flex;
  align-items: center;
  flex: 1;
  font-size: 0.875rem;
  color: #374151;
}

.config-name {
  font-weight: 600;
  color: #1f2937;
}

.separator {
  margin: 0 2px;
  color: #9ca3af;
}

.model-name {
  color: #6b7280;
}

.placeholder {
  color: #9ca3af;
  font-size: 0.875rem;
}

.dropdown-icon {
  font-size: 1.25rem;
  color: #9ca3af;
  transition: transform 0.2s ease;
  
  .is-open & {
    transform: rotate(180deg);
  }
}

.model-select-dropdown {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  z-index: 50;
  margin-bottom: 0.25rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  width: fit-content;
}

.dropdown-content {
  max-height: 200px;
  overflow-y: auto;
}

.model-option {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  white-space: nowrap;
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: #f9fafb;
  }
  
  &.is-selected {
    background: #eff6ff;
    color: #1d4ed8;
    
    .config-name {
      color: #1d4ed8;
    }
  }
  
  .config-name {
    font-weight: 600;
    color: #1f2937;
  }
  
  .separator {
    margin: 0 0.5rem;
    color: #9ca3af;
  }
  
  .model-name {
    color: #6b7280;
  }
  
  .fallback-name {
    color: #374151;
  }
}

/* 下拉动画 - 向上展开 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.dropdown-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
